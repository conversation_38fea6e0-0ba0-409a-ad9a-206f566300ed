#Requires -Version 5.0

<#
.SY<PERSON><PERSON><PERSON>S
    Creates a trigger file to initiate ScheduledStaging.ps1 execution
    
.DESCRI<PERSON><PERSON><PERSON>
    Creates a trigger file that the TriggerStagingMonitor script monitors for.
    When this file is created, it triggers the execution of ScheduledStaging.ps1.
    Optionally includes parameters to pass to the staging script.
    
.PARAMETER TriggerFilePath
    Path where the trigger file should be created
    
.PARAMETER Message
    Optional message to include in the trigger file
    
.PARAMETER StagingParameters
    Optional hashtable of parameters to pass to ScheduledStaging.ps1
    
.PARAMETER Force
    Overwrite existing trigger file if it exists
    
.EXAMPLE
    .\Create-StagingTrigger.ps1 -TriggerFilePath "\\server\share\STAGING_TRIGGER.txt" -Message "Process staging files"
    
.EXAMPLE
    .\Create-StagingTrigger.ps1 -StagingParameters @{Environment="TEST"; BatchSize=100} -Message "Test staging run"
#>

param(
    [Parameter(Mandatory=$false)]
    [string]$TriggerFilePath = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\STAGING_TRIGGER.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$Message = "Trigger file created to initiate ScheduledStaging.ps1 execution",
    
    [Parameter(Mandatory=$false)]
    [hashtable]$StagingParameters = @{},
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Output "[$Timestamp] [$Level] $Message"
}

function Format-Parameters {
    param([hashtable]$Parameters)
    
    if ($Parameters.Count -eq 0) {
        return ""
    }
    
    $ParamStrings = @()
    foreach ($Key in $Parameters.Keys) {
        $ParamStrings += "$Key=$($Parameters[$Key])"
    }
    
    return "PARAMETERS:`n" + ($ParamStrings -join "`n")
}

try {
    Write-Log "Creating staging trigger file: $TriggerFilePath"
    
    # Check if trigger file already exists
    if ((Test-Path $TriggerFilePath) -and -not $Force) {
        Write-Log "Trigger file already exists. Use -Force to overwrite." "WARN"
        $Overwrite = Read-Host "Overwrite existing trigger file? (y/n)"
        if ($Overwrite -ne 'y' -and $Overwrite -ne 'Y') {
            Write-Log "Operation cancelled by user"
            exit 0
        }
    }
    
    # Ensure directory exists
    $TriggerDir = Split-Path $TriggerFilePath -Parent
    if (-not (Test-Path $TriggerDir)) {
        Write-Log "Creating trigger directory: $TriggerDir"
        New-Item -Path $TriggerDir -ItemType Directory -Force | Out-Null
    }
    
    # Create trigger file content
    $TriggerContent = @"
STAGING TRIGGER FILE
===================
Created: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Created By: $env:USERNAME on $env:COMPUTERNAME
Process ID: $PID
Message: $Message

This file triggers the execution of ScheduledStaging.ps1.
The file will be moved to the ProcessedTriggers directory after processing.

$(Format-Parameters -Parameters $StagingParameters)

--- END OF TRIGGER FILE ---
"@
    
    # Write trigger file
    $TriggerContent | Out-File -FilePath $TriggerFilePath -Encoding UTF8 -Force
    
    Write-Log "Trigger file created successfully"
    Write-Log "Message: $Message"
    
    if ($StagingParameters.Count -gt 0) {
        Write-Log "Parameters to pass to ScheduledStaging.ps1:"
        foreach ($Key in $StagingParameters.Keys) {
            Write-Log "  $Key = $($StagingParameters[$Key])"
        }
    }
    
    # Verify file was created
    if (Test-Path $TriggerFilePath) {
        $FileInfo = Get-Item $TriggerFilePath
        Write-Log "File size: $($FileInfo.Length) bytes"
        Write-Log "File created: $($FileInfo.CreationTime)"
        
        # Display file content for verification
        Write-Log "Trigger file content:"
        Write-Log "===================="
        Get-Content $TriggerFilePath | ForEach-Object { Write-Log $_ }
        Write-Log "===================="
    } else {
        throw "Failed to create trigger file"
    }
    
    Write-Log "Trigger file creation completed successfully"
    Write-Log "The TriggerStagingMonitor will detect this file and execute ScheduledStaging.ps1"
    
} catch {
    Write-Log "Error creating trigger file: $($_.Exception.Message)" "ERROR"
    exit 1
}
