#Requires -Version 5.0

<#
.SY<PERSON><PERSON><PERSON>S
    Creates a trigger file for ScheduledStaging.ps1
    
.DESCRIP<PERSON><PERSON>
    Creates a STAGING_TRIGGER.txt file in the specified source directory
    to trigger the ScheduledStaging.ps1 script execution.
    
.PARAMETER SourceDirectory
    The source directory where the trigger file should be created
    
.PARAMETER Message
    Optional message to include in the trigger file
    
.EXAMPLE
    .\Create-StagingTriggerFile.ps1 -SourceDirectory "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\"
    
.EXAMPLE
    .\Create-StagingTriggerFile.ps1 -SourceDirectory "\\server\share\source\" -Message "Manual trigger for testing"
#>

param(
    [Parameter(Mandatory=$false)]
    [string]$SourceDirectory = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\",
    
    [Parameter(Mandatory=$false)]
    [string]$Message = "Trigger file created to initiate ScheduledStaging.ps1 execution"
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Output "[$Timestamp] [$Level] $Message"
}

try {
    Write-Log "Creating staging trigger file..."
    
    # Ensure source directory exists
    if (-not (Test-Path $SourceDirectory)) {
        Write-Log "Source directory does not exist: $SourceDirectory" "ERROR"
        exit 1
    }
    
    # Define trigger file path
    $TriggerFileName = "STAGING_TRIGGER.txt"
    $TriggerFilePath = Join-Path $SourceDirectory $TriggerFileName
    
    # Check if trigger file already exists
    if (Test-Path $TriggerFilePath) {
        Write-Log "Trigger file already exists: $TriggerFilePath" "WARN"
        $Overwrite = Read-Host "Overwrite existing trigger file? (y/n)"
        if ($Overwrite -ne 'y' -and $Overwrite -ne 'Y') {
            Write-Log "Operation cancelled by user"
            exit 0
        }
    }
    
    # Create trigger file content
    $TriggerContent = @"
STAGING TRIGGER FILE
===================
Created: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Created By: $env:USERNAME on $env:COMPUTERNAME
Process ID: $PID
Message: $Message

This file triggers the execution of ScheduledStaging.ps1.
The file will be moved to the TriggerProcessed subdirectory after processing.

Instructions:
1. This trigger file indicates that staging should begin
2. ScheduledStaging.ps1 will detect this file
3. The file will be moved to TriggerProcessed with a timestamp
4. Normal staging operations will then proceed

--- END OF TRIGGER FILE ---
"@
    
    # Write trigger file
    $TriggerContent | Out-File -FilePath $TriggerFilePath -Encoding UTF8 -Force
    
    Write-Log "Trigger file created successfully: $TriggerFilePath"
    Write-Log "Message: $Message"
    
    # Verify file was created
    if (Test-Path $TriggerFilePath) {
        $FileInfo = Get-Item $TriggerFilePath
        Write-Log "File size: $($FileInfo.Length) bytes"
        Write-Log "File created: $($FileInfo.CreationTime)"
        
        Write-Log "Trigger file is ready for ScheduledStaging.ps1 to process"
        Write-Log "The next run of ScheduledStaging.ps1 will detect this file and begin staging"
    } else {
        Write-Log "Failed to create trigger file" "ERROR"
        exit 1
    }
    
} catch {
    Write-Log "Error creating trigger file: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Log "Trigger file creation completed successfully"
