#Requires -Version 5.0

<#
.S<PERSON><PERSON><PERSON><PERSON><PERSON>
    Creates a trigger file to initiate file processing
    
.DESCRIP<PERSON><PERSON>
    Creates a trigger file that the TriggerFileProcessor script monitors for.
    When this file is created, it triggers the file move process.
    
.PARAMETER TriggerFilePath
    Path where the trigger file should be created
    
.PARAMETER Message
    Optional message to include in the trigger file
    
.EXAMPLE
    .\Create-TriggerFile.ps1 -TriggerFilePath "\\server\share\PROCESS_TRIGGER.txt" -Message "Process TMA files"
#>

param(
    [Parameter(Mandatory=$false)]
    [string]$TriggerFilePath = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\PROCESS_TRIGGER.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$Message = "Trigger file created to initiate TMA file processing"
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Output "[$Timestamp] [$Level] $Message"
}

try {
    Write-Log "Creating trigger file: $TriggerFilePath"
    
    # Ensure directory exists
    $TriggerDir = Split-Path $TriggerFilePath -Parent
    if (-not (Test-Path $TriggerDir)) {
        Write-Log "Creating trigger directory: $TriggerDir"
        New-Item -Path $TriggerDir -ItemType Directory -Force | Out-Null
    }
    
    # Create trigger file content
    $TriggerContent = @"
Trigger File Created: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Created By: $env:USERNAME on $env:COMPUTERNAME
Message: $Message
Process ID: $PID

This file triggers the TMA file processing workflow.
The file will be moved to the ProcessedTriggers directory after processing.
"@
    
    # Write trigger file
    $TriggerContent | Out-File -FilePath $TriggerFilePath -Encoding UTF8 -Force
    
    Write-Log "Trigger file created successfully"
    Write-Log "Content: $Message"
    
    # Verify file was created
    if (Test-Path $TriggerFilePath) {
        $FileInfo = Get-Item $TriggerFilePath
        Write-Log "File size: $($FileInfo.Length) bytes"
        Write-Log "File created: $($FileInfo.CreationTime)"
    } else {
        throw "Failed to create trigger file"
    }
    
} catch {
    Write-Log "Error creating trigger file: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Log "Trigger file creation completed successfully"
