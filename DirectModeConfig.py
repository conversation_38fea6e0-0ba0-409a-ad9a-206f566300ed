IDRWebServer = "vwmazidr2023q2.fisdev.local"

#postgres database connection
PostgresServer = 'vwmazpssql.fisdev.local'
PostgresPort = '5432'
PostgresControlDatabase = 'controldb_idr'
PostgresResultsDatabase = 'resultsdb_idr'
PostgresUsername = 'postgres'
Postgrespassword_encrypted = ****************************************iVGTiWgoCFbq3nge5Cujv4eb3IY1gtlKg9NEP2bESxo910SO8nZGaUBiqx2A=='
Postgrespassword_encryptKey = ****************************************1SO3Q='

# Snowflake connection parameters
SnowflakeAccount = 'FISDEV.us-east-1'  # Format: orgname-accountname (e.g., xy12345.us-east-1)
SnowflakeWarehouse = 'CAPITAL_MARKETS_IDR__WH'
SnowflakeControlDatabase = 'controldb_idr'
SnowflakeResultsDatabase = 'resultsdb_idr'
SnowflakeSchema = 'IDR'
SnowflakeRole = 'SERV_RL__SERVICE_IDR_REPORTING'

# Snowflake Authentication
SnowflakeUsername = 'SERVICE_IDR_REPORTING'
SnowflakePassword = '**************'  # Store encrypted or use key vault
SnowflakePassword_encrypted = b''
SnowflakePassword_encryptKey = b''


IDRControlDbSchema = 'idr'
IDRResultsDbSchema = 'idr'

# yes/no check request progress after send new request
check_request_progress ='yes'
max_check_iteration = 600
check_request_wait_time = 10


