import requests
import json
from datetime import date, datetime
from colorama import init, Fore, Style
import pandas as pd
import pyodbc
import os
import DirectModeConfig as config


def writeLog(logFile,msg_type,msg_log):
    init()
    if (msg_type == 'Error'):
        print (Fore.RED + msg_log)
    elif(msg_type == 'Step Info'):
        print(Fore.GREEN + msg_log)
    elif(msg_type == 'Success'):
        print(Fore.YELLOW + msg_log)
    else:
        print(Style.RESET_ALL +  msg_log)
    lines = ['', "{0} - {1} - {2:9}{3}".format(datetime.today().strftime("%H:%M:%S"), os.getlogin(), msg_type, msg_log)]
    with open(logFile , 'a') as f:
        f.writelines('\n'.join(lines))

def IDRApiCall(method,url,body):
    request_method = getattr(requests, method.lower())
    response=request_method(url,json=body,headers = {"Content-Type": "application/vnd.api+json"})
    return response

def generateRequestFilters (fileNames,runNumbers,products,resultsTypes,spCodes,simulations,aggregationIdentifiers,aggregationNames,aggregationVariables,policyIdentifiers,monthFirst,monthTimes,yearFirst,yearTimes):
    requestFilters = '''"requestFilters": {
    '''
    if (fileNames):
        requestFilters = requestFilters + '''
            "fileNames": ''' + json.dumps(fileNames) + ''','''
    if (runNumbers):
        requestFilters = requestFilters + '''
            "runNumbers": ''' + json.dumps(runNumbers) + ''','''
    if (products):
        requestFilters = requestFilters + '''
            "products": ''' + json.dumps(products) + ''','''
    if (resultsTypes):
        requestFilters = requestFilters + '''
            "resultsTypes": ''' + json.dumps(resultsTypes) + ''',''' 
    if (spCodes):
        requestFilters = requestFilters + '''
            "spCodes": ''' + json.dumps(spCodes) + ''','''
    if (simulations):
        requestFilters = requestFilters + '''
            "simulations": ''' + json.dumps(simulations) + ''','''    
    if (aggregationIdentifiers):
        requestFilters = requestFilters + '''
            "aggregationIdentifiers": "''' + aggregationIdentifiers + '''",'''
    if (aggregationNames):
        requestFilters = requestFilters + '''
            "aggregationNames": "''' + aggregationNames + '''",'''
    if (aggregationVariables):
        requestFilters = requestFilters + '''
            "aggregationVariables": "''' + aggregationVariables + '''",'''
    if (policyIdentifiers):
        requestFilters = requestFilters + '''
            "policyIdentifiers": "''' + policyIdentifiers + '''",'''
    
    if requestFilters.endswith(','):
        requestFilters = requestFilters[:-1]
    requestFilters = requestFilters + '''
            },'''
    
    if (monthFirst):
        requestFilters = requestFilters + '''
            "monthFirst": ''' + str(monthFirst) + ''','''
    if (monthTimes):
        requestFilters = requestFilters + '''
            "monthTimes": ''' + str(monthTimes) + ''','''
    if (yearFirst):
        requestFilters = requestFilters + '''
            "yearFirst": ''' + str(yearFirst) + ''','''
    if (yearTimes):
        requestFilters = requestFilters + '''
            "yearTimes": ''' + str(yearTimes) + ''','''
    
    if requestFilters.endswith(','):
        requestFilters = requestFilters[:-1]
        
    return requestFilters

def generateCsvRequestFilters (fileNames):
    CsvRequestFilters = '''"requestFilters": {'''
    if (fileNames):
        CsvRequestFilters = CsvRequestFilters + '''
            "fileNames": ''' + json.dumps(fileNames) + ''','''
    
    if CsvRequestFilters.endswith(','):
        CsvRequestFilters = CsvRequestFilters[:-1]
    CsvRequestFilters = CsvRequestFilters + '''
            }'''
    
    return CsvRequestFilters
    
def generateCsvParameters(ListSeperator,NumberSeperator,CurrentCulture,Quote):
    CsvParameters = '''"requestParameters":{"subType":"CSV"'''
    if (ListSeperator):
        CsvParameters = CsvParameters + ''',"listSeperator":"''' + ListSeperator + '''"'''
    if (NumberSeperator):
        CsvParameters = CsvParameters + ''',"numberSeperator":"''' + NumberSeperator + '''"'''
    if (CurrentCulture):
        CsvParameters = CsvParameters + ''',"currentCulture":"''' + CurrentCulture + '''"'''
    if (Quote):
        CsvParameters = CsvParameters + ''',"quote":"\\''' + Quote + '''"'''   
    CsvParameters = CsvParameters + '''}'''
    return CsvParameters

def createRequest (requestDescription,resultsLocation,requestFilters,logFile,url):
    timeStr = datetime.now().strftime('%Y-%m-%dT%H:%M:%S%z')
    method = 'post'
    request_body = '''{
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "''' + requestDescription +'''",
            "resultsLocation": "''' + resultsLocation.replace('\\','\\\\') +'''",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "''' + timeStr +'''",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            ''' + requestFilters + '''
        }
    }
}'''  

    writeLog (logFile,'info','request body: ' + request_body)
    response=IDRApiCall(method,url,json.loads(request_body))
    
    return response
    
def createCsvRequest (requestDescription,sourcesLocation,CsvRequestFilters,CsvParameters,logFile,url):
    timeStr = datetime.now().strftime('%Y-%m-%dT%H:%M:%S%z')
    method = 'post'
    request_body = '''{
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "''' + requestDescription +'''",
            "resultsLocation": "''' + sourcesLocation.replace('\\','\\\\') +'''",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "''' + timeStr +'''",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            ''' + CsvRequestFilters + ''',
            ''' + CsvParameters + '''
        }
    }
}'''  

    writeLog (logFile,'info','request body: ' + request_body)
    response=IDRApiCall(method,url,json.loads(request_body))
    
    return response
