import requests
import json
from datetime import date, datetime
from colorama import Fore
import time
import pandas as pd
import snowflake.connector
import os
import sys
import importlib
import re
import csv
from sqlalchemy import create_engine,text
import urllib.parse
from snowflake.connector.pandas_tools import write_pandas
from cryptography.fernet import Fernet
import DirectModeConfig as config
import DirectModeModules as fn

workspace = os.path.dirname(os.path.abspath(__file__)) 
logFile = os.path.join(workspace, 'LogFiles', 'DirectModeRunScript' + date.today().strftime('%Y%m%d') + '.txt')
SourcesFolder = os.path.join(workspace, 'Sources')
ReportsFolder = os.path.join(workspace, 'Reports')

# If using encrypted password
if config.SnowflakePassword_encrypted:
    snowflakePassword = Fernet(config.SnowflakePassword_encryptKey).decrypt(config.SnowflakePassword_encrypted).decode("utf-8")
else:
    snowflakePassword = config.SnowflakePassword

# URL encode the password to handle special characters
encoded_password = urllib.parse.quote_plus(snowflakePassword)

# Create connection strings
snowflakeControlDbConnectionString = (
    f"snowflake://{config.SnowflakeUsername}:{encoded_password}@"
    f"{config.SnowflakeAccount}/{config.SnowflakeControlDatabase}/"
    f"{config.SnowflakeSchema}?"
    f"warehouse={config.SnowflakeWarehouse}&"
    f"role={config.SnowflakeRole}"
)

snowflakeResultsDbConnectionString = (
    f"snowflake://{config.SnowflakeUsername}:{encoded_password}@"
    f"{config.SnowflakeAccount}/{config.SnowflakeResultsDatabase}/"
    f"{config.SnowflakeSchema}?"
    f"warehouse={config.SnowflakeWarehouse}&"
    f"role={config.SnowflakeRole}"
)

# Create engine with connect_args for additional configuration
engine_args = {
    'connect_args': {
        'account': config.SnowflakeAccount,
        'user': config.SnowflakeUsername,
        'password': snowflakePassword,
        'warehouse': config.SnowflakeWarehouse,
        'database': config.SnowflakeControlDatabase,
        'schema': config.SnowflakeSchema,
        'role': config.SnowflakeRole
    }
}

try:
    # Create the engine with explicit parameters
    SnowflakeControlDbConnect = create_engine(
        'snowflake://',
        **engine_args
    )
    
    # Test the connection
    with SnowflakeControlDbConnect.connect() as conn:
        result = conn.execute(text('SELECT CURRENT_WAREHOUSE(), CURRENT_DATABASE(), CURRENT_SCHEMA()')).fetchone()
        fn.writeLog(logFile, 'Info', f'Connected successfully to Snowflake - Warehouse: {result[0]}, Database: {result[1]}, Schema: {result[2]}')

except Exception as e:
    fn.writeLog(logFile, 'Error', f'Failed to connect to Snowflake: {str(e)}')
    raise