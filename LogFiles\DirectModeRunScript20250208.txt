
01:30:19 - e5689845 - Step Inforeading results settings
01:30:19 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Sources
01:30:19 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
01:30:19 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
01:30:19 - e5689845 - Info     resultsLoadingFlag: no
01:30:19 - e5689845 - Info     csvLoadingFlag: yes
01:30:19 - e5689845 - Info     extractionFlag: yes
01:30:19 - e5689845 - Info     update_t_AggPrjData: no
01:30:19 - e5689845 - Info     update_t_IMPData: no
01:30:19 - e5689845 - Info     update_t_PRJData: yes
01:30:19 - e5689845 - Info     update_t_StoMData: no
01:30:19 - e5689845 - Info     update_t_StoYData: no
01:30:19 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
    
            "fileNames": ["Ref*.csv"]
            }
01:30:19 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV",ListSeperator":",",NumberSeperator":".",Quote":"\""}
01:30:19 - e5689845 - Step InfoCreating new csv load request
01:30:19 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
01:30:19 - e5689845 - Error    Error: name 'url' is not defined
01:38:13 - e5689845 - Step Inforeading results settings
01:38:13 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Sources
01:38:13 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
01:38:13 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
01:38:13 - e5689845 - Info     resultsLoadingFlag: no
01:38:13 - e5689845 - Info     csvLoadingFlag: yes
01:38:13 - e5689845 - Info     extractionFlag: yes
01:38:13 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
01:38:13 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
01:38:13 - e5689845 - Step InfoCreating new csv load request
01:38:13 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
01:38:13 - e5689845 - Error    Error: name 'url' is not defined
01:39:04 - e5689845 - Step Inforeading results settings
01:39:04 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Sources
01:39:04 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
01:39:04 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
01:39:04 - e5689845 - Info     resultsLoadingFlag: no
01:39:04 - e5689845 - Info     csvLoadingFlag: yes
01:39:04 - e5689845 - Info     extractionFlag: yes
01:39:04 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
01:39:04 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
01:39:04 - e5689845 - Step InfoCreating new csv load request
01:39:04 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
01:39:04 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-08T01:39:04",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
01:39:07 - e5689845 - Info     <Response [201]>
01:53:35 - e5689845 - Step Inforeading results settings
01:53:35 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
01:53:35 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
01:53:35 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
01:53:35 - e5689845 - Info     resultsLoadingFlag: no
01:53:35 - e5689845 - Info     csvLoadingFlag: yes
01:53:35 - e5689845 - Info     extractionFlag: yes
01:53:35 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
01:53:35 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
01:53:35 - e5689845 - Step InfoCreating new csv load request
01:53:35 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
01:53:35 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-08T01:53:35",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
01:53:35 - e5689845 - Info     <Response [201]>
06:52:21 - e5689845 - Step Inforeading results settings
06:52:21 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
06:52:21 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
06:52:21 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
06:52:21 - e5689845 - Info     resultsLoadingFlag: no
06:52:21 - e5689845 - Info     csvLoadingFlag: yes
06:52:21 - e5689845 - Info     extractionFlag: yes
06:52:21 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
06:52:21 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
06:52:21 - e5689845 - Step InfoCreating new csv load request
06:52:21 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
06:52:21 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-08T06:52:21",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
06:52:23 - e5689845 - Info     <Response [201]>