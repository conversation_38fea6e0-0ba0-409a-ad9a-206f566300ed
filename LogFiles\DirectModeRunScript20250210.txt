
01:28:06 - e5689845 - Step Inforeading results settings
01:28:06 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
01:28:06 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
01:28:06 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
01:28:06 - e5689845 - Info     resultsLoadingFlag: yes
01:28:06 - e5689845 - Info     csvLoadingFlag: yes
01:28:06 - e5689845 - Info     extractionFlag: yes
01:28:06 - e5689845 - Info     update_t_AggPrjData: no
01:28:06 - e5689845 - Info     update_t_IMPData: no
01:28:06 - e5689845 - Info     update_t_PRJData: yes
01:28:06 - e5689845 - Info     update_t_StoMData: no
01:28:06 - e5689845 - Info     update_t_StoYData: no
01:28:06 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
01:28:06 - e5689845 - Info     variable_filter: ''
01:28:06 - e5689845 - Step InfoCreating new load request
01:28:06 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
01:28:06 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T01:28:06",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
        }
    }
}
01:28:09 - e5689845 - Info     <Response [201]>
01:28:09 - e5689845 - Step Infoconnecting to postgres control database
01:28:09 - e5689845 - Info     connected to controldb_idr
01:28:14 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
01:28:14 - e5689845 - Info     requestid: 87
01:28:14 - e5689845 - Info     requestdescription: csvTest_RP202501_Step2
01:28:14 - e5689845 - Info     request (87) in progress (status 3)
01:28:14 - e5689845 - Info     wait 10s for next status check
01:28:24 - e5689845 - Info     request (87) in progress (status 3)
01:28:24 - e5689845 - Info     wait 10s for next status check
01:28:34 - e5689845 - Info     request (87) in progress (status 4)
01:28:34 - e5689845 - Info     wait 10s for next status check
01:28:44 - e5689845 - Info     request (87) in progress (status 4)
01:28:44 - e5689845 - Info     wait 10s for next status check
01:28:54 - e5689845 - Info     request (87) in progress (status 4)
01:28:54 - e5689845 - Info     wait 10s for next status check
01:29:04 - e5689845 - Info     request (87) in progress (status 4)
01:29:04 - e5689845 - Info     wait 10s for next status check
01:29:14 - e5689845 - Info     request (87) in progress (status 4)
01:29:14 - e5689845 - Info     wait 10s for next status check
01:29:24 - e5689845 - Info     request (87) in progress (status 4)
01:29:24 - e5689845 - Info     wait 10s for next status check
01:29:34 - e5689845 - Info     request (87) in progress (status 4)
01:29:34 - e5689845 - Info     wait 10s for next status check
01:29:44 - e5689845 - Info     request (87) in progress (status 4)
01:29:44 - e5689845 - Info     wait 10s for next status check
01:29:54 - e5689845 - Info     request (87) in progress (status 4)
01:29:54 - e5689845 - Info     wait 10s for next status check
01:30:04 - e5689845 - Info     request (87) in progress (status 4)
01:30:04 - e5689845 - Info     wait 10s for next status check
01:30:14 - e5689845 - Info     request (87) in progress (status 4)
01:30:14 - e5689845 - Info     wait 10s for next status check
01:30:24 - e5689845 - Info     request (87) in progress (status 4)
01:30:24 - e5689845 - Info     wait 10s for next status check
01:30:34 - e5689845 - Info     request (87) in progress (status 4)
01:30:35 - e5689845 - Info     wait 10s for next status check
01:30:45 - e5689845 - Info     request (87) in progress (status 4)
01:30:45 - e5689845 - Info     wait 10s for next status check
01:30:55 - e5689845 - Info     request (87) in progress (status 4)
01:30:55 - e5689845 - Info     wait 10s for next status check
01:31:05 - e5689845 - Info     request (87) in progress (status 4)
01:31:05 - e5689845 - Info     wait 10s for next status check
01:31:15 - e5689845 - Info     request (87) in progress (status 4)
01:31:15 - e5689845 - Info     wait 10s for next status check
01:31:25 - e5689845 - Info     request (87) in progress (status 4)
01:31:25 - e5689845 - Info     wait 10s for next status check
01:31:35 - e5689845 - Info     request (87) in progress (status 4)
01:31:35 - e5689845 - Info     wait 10s for next status check
01:31:45 - e5689845 - Info     request (87) in progress (status 4)
01:31:45 - e5689845 - Info     wait 10s for next status check
01:31:55 - e5689845 - Info     request (87) in progress (status 4)
01:31:55 - e5689845 - Info     wait 10s for next status check
01:32:05 - e5689845 - Info     request (87) in progress (status 4)
01:32:05 - e5689845 - Info     wait 10s for next status check
01:32:15 - e5689845 - Info     request (87) in progress (status 4)
01:32:15 - e5689845 - Info     wait 10s for next status check
01:32:25 - e5689845 - Info     request (87) in progress (status 4)
01:32:25 - e5689845 - Info     wait 10s for next status check
01:32:35 - e5689845 - Info     request (87) in progress (status 4)
01:32:35 - e5689845 - Info     wait 10s for next status check
01:32:45 - e5689845 - Info     request (87) in progress (status 4)
01:32:45 - e5689845 - Info     wait 10s for next status check
01:32:55 - e5689845 - Info     request (87) in progress (status 4)
01:32:55 - e5689845 - Info     wait 10s for next status check
01:33:05 - e5689845 - Info     request (87) in progress (status 4)
01:33:05 - e5689845 - Info     wait 10s for next status check
01:33:15 - e5689845 - Info     request (87) in progress (status 4)
01:33:15 - e5689845 - Info     wait 10s for next status check
01:33:25 - e5689845 - Info     request (87) in progress (status 4)
01:33:25 - e5689845 - Info     wait 10s for next status check
01:33:35 - e5689845 - Info     request (87) in progress (status 4)
01:33:35 - e5689845 - Info     wait 10s for next status check
01:33:45 - e5689845 - Info     request (87) in progress (status 6)
01:33:45 - e5689845 - Error    request failed, please check db log for more information
01:44:08 - e5689845 - Step Inforeading results settings
01:44:08 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
01:44:08 - e5689845 - Info     requestDescription: ALS_RP202501_Step2
01:44:08 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
01:44:08 - e5689845 - Info     resultsLoadingFlag: yes
01:44:08 - e5689845 - Info     csvLoadingFlag: yes
01:44:08 - e5689845 - Info     extractionFlag: yes
01:44:08 - e5689845 - Info     update_t_AggPrjData: no
01:44:08 - e5689845 - Info     update_t_IMPData: no
01:44:08 - e5689845 - Info     update_t_PRJData: yes
01:44:08 - e5689845 - Info     update_t_StoMData: no
01:44:08 - e5689845 - Info     update_t_StoYData: no
01:44:08 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
01:44:08 - e5689845 - Info     variable_filter: ''
01:44:09 - e5689845 - Step InfoCreating new load request
01:44:09 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
01:44:09 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "ALS_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T01:44:09",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
        }
    }
}
01:44:09 - e5689845 - Info     <Response [201]>
01:44:09 - e5689845 - Step Infoconnecting to postgres control database
01:44:09 - e5689845 - Info     connected to controldb_idr
01:44:14 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
01:44:14 - e5689845 - Info     requestid: 88
01:44:14 - e5689845 - Info     requestdescription: ALS_RP202501_Step2
01:44:14 - e5689845 - Info     request (88) in progress (status 3)
01:44:14 - e5689845 - Info     wait 10s for next status check
01:44:24 - e5689845 - Info     request (88) in progress (status 3)
01:44:24 - e5689845 - Info     wait 10s for next status check
01:44:34 - e5689845 - Info     request (88) in progress (status 4)
01:44:34 - e5689845 - Info     wait 10s for next status check
01:44:44 - e5689845 - Info     request (88) in progress (status 4)
01:44:44 - e5689845 - Info     wait 10s for next status check
01:44:54 - e5689845 - Info     request (88) in progress (status 4)
01:44:54 - e5689845 - Info     wait 10s for next status check
01:45:04 - e5689845 - Info     request (88) in progress (status 4)
01:45:04 - e5689845 - Info     wait 10s for next status check
01:45:14 - e5689845 - Info     request (88) in progress (status 4)
01:45:14 - e5689845 - Info     wait 10s for next status check
01:45:24 - e5689845 - Info     request (88) in progress (status 4)
01:45:24 - e5689845 - Info     wait 10s for next status check
01:45:34 - e5689845 - Info     request (88) in progress (status 4)
01:45:34 - e5689845 - Info     wait 10s for next status check
01:45:44 - e5689845 - Info     request (88) in progress (status 4)
01:45:44 - e5689845 - Info     wait 10s for next status check
01:45:54 - e5689845 - Info     request (88) in progress (status 4)
01:45:54 - e5689845 - Info     wait 10s for next status check
01:46:04 - e5689845 - Info     request (88) in progress (status 4)
01:46:04 - e5689845 - Info     wait 10s for next status check
01:46:14 - e5689845 - Info     request (88) in progress (status 4)
01:46:14 - e5689845 - Info     wait 10s for next status check
01:46:24 - e5689845 - Info     request (88) in progress (status 4)
01:46:24 - e5689845 - Info     wait 10s for next status check
01:46:34 - e5689845 - Info     request (88) in progress (status 4)
01:46:34 - e5689845 - Info     wait 10s for next status check
01:46:44 - e5689845 - Info     request (88) in progress (status 4)
01:46:44 - e5689845 - Info     wait 10s for next status check
01:46:54 - e5689845 - Info     request (88) in progress (status 4)
01:46:54 - e5689845 - Info     wait 10s for next status check
01:47:04 - e5689845 - Info     request (88) in progress (status 4)
01:47:04 - e5689845 - Info     wait 10s for next status check
01:47:14 - e5689845 - Info     request (88) in progress (status 4)
01:47:14 - e5689845 - Info     wait 10s for next status check
01:47:24 - e5689845 - Info     request (88) in progress (status 4)
01:47:24 - e5689845 - Info     wait 10s for next status check
01:47:34 - e5689845 - Info     request (88) in progress (status 4)
01:47:34 - e5689845 - Info     wait 10s for next status check
01:47:44 - e5689845 - Info     request (88) in progress (status 4)
01:47:44 - e5689845 - Info     wait 10s for next status check
01:47:54 - e5689845 - Info     request (88) in progress (status 4)
01:47:54 - e5689845 - Info     wait 10s for next status check
01:48:04 - e5689845 - Info     request (88) in progress (status 4)
01:48:04 - e5689845 - Info     wait 10s for next status check
01:48:14 - e5689845 - Info     request (88) in progress (status 4)
01:48:14 - e5689845 - Info     wait 10s for next status check
01:48:24 - e5689845 - Info     request (88) in progress (status 4)
01:48:24 - e5689845 - Info     wait 10s for next status check
01:48:34 - e5689845 - Info     request (88) in progress (status 4)
01:48:34 - e5689845 - Info     wait 10s for next status check
01:48:44 - e5689845 - Info     request (88) in progress (status 4)
01:48:44 - e5689845 - Info     wait 10s for next status check
01:48:54 - e5689845 - Info     request (88) in progress (status 4)
01:48:54 - e5689845 - Info     wait 10s for next status check
01:49:04 - e5689845 - Info     request (88) in progress (status 4)
01:49:04 - e5689845 - Info     wait 10s for next status check
01:49:14 - e5689845 - Info     request (88) in progress (status 4)
01:49:14 - e5689845 - Info     wait 10s for next status check
01:49:24 - e5689845 - Info     request (88) in progress (status 4)
01:49:24 - e5689845 - Info     wait 10s for next status check
01:49:34 - e5689845 - Info     request (88) in progress (status 4)
01:49:34 - e5689845 - Info     wait 10s for next status check
01:49:44 - e5689845 - Info     request (88) in progress (status 4)
01:49:44 - e5689845 - Info     wait 10s for next status check
01:49:54 - e5689845 - Info     request (88) in progress (status 6)
01:49:54 - e5689845 - Error    request failed, please check db log for more information
01:50:47 - e5689845 - Step Inforeading results settings
01:50:47 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
01:50:47 - e5689845 - Info     requestDescription: ALS_RP202501_Step2
01:50:47 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
01:50:47 - e5689845 - Info     resultsLoadingFlag: no
01:50:47 - e5689845 - Info     csvLoadingFlag: yes
01:50:47 - e5689845 - Info     extractionFlag: yes
01:50:47 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
01:50:47 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
01:50:47 - e5689845 - Step InfoCreating new csv load request
01:50:47 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
01:50:47 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "ALS_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T01:50:47",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
01:50:47 - e5689845 - Info     <Response [201]>
02:33:55 - e5689845 - Step Inforeading results settings
02:33:55 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
02:33:55 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
02:33:55 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
02:33:55 - e5689845 - Info     resultsLoadingFlag: no
02:33:55 - e5689845 - Info     csvLoadingFlag: yes
02:33:55 - e5689845 - Info     extractionFlag: yes
02:33:55 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
02:33:55 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
02:33:55 - e5689845 - Step InfoCreating new csv load request
02:33:55 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
02:33:55 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T02:33:55",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
02:33:55 - e5689845 - Info     <Response [201]>
02:43:00 - e5689845 - Step Inforeading results settings
02:43:00 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
02:43:00 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
02:43:00 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
02:43:00 - e5689845 - Info     resultsLoadingFlag: no
02:43:00 - e5689845 - Info     csvLoadingFlag: yes
02:43:00 - e5689845 - Info     extractionFlag: yes
02:43:00 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
02:43:00 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
02:43:00 - e5689845 - Step InfoCreating new csv load request
02:43:00 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
02:43:00 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T02:43:00",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
02:43:00 - e5689845 - Info     <Response [201]>
02:45:29 - e5689845 - Step Inforeading results settings
02:45:29 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
02:45:29 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
02:45:29 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
02:45:29 - e5689845 - Info     resultsLoadingFlag: no
02:45:29 - e5689845 - Info     csvLoadingFlag: yes
02:45:29 - e5689845 - Info     extractionFlag: yes
02:45:29 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
02:45:29 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
02:45:29 - e5689845 - Step InfoCreating new csv load request
02:45:29 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
02:45:29 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T02:45:29",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
02:45:29 - e5689845 - Info     <Response [201]>
02:49:02 - e5689845 - Step Inforeading results settings
02:49:02 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
02:49:02 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
02:49:02 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
02:49:02 - e5689845 - Info     resultsLoadingFlag: no
02:49:02 - e5689845 - Info     csvLoadingFlag: yes
02:49:02 - e5689845 - Info     extractionFlag: yes
02:49:02 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
02:49:03 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
02:49:03 - e5689845 - Step InfoCreating new csv load request
02:49:03 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
02:49:03 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T02:49:03",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
02:49:03 - e5689845 - Info     <Response [201]>
03:37:36 - e5689845 - Step Inforeading results settings
03:37:36 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
03:37:36 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
03:37:36 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
03:37:36 - e5689845 - Info     resultsLoadingFlag: no
03:37:36 - e5689845 - Info     csvLoadingFlag: yes
03:37:36 - e5689845 - Info     extractionFlag: yes
03:37:36 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
03:37:36 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
03:37:36 - e5689845 - Step InfoCreating new csv load request
03:37:36 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
03:37:36 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T03:37:36",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
03:37:39 - e5689845 - Info     <Response [201]>
04:03:45 - e5689845 - Step Inforeading results settings
04:03:45 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
04:03:45 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
04:03:45 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
04:03:45 - e5689845 - Info     resultsLoadingFlag: yes
04:03:45 - e5689845 - Info     csvLoadingFlag: yes
04:03:45 - e5689845 - Info     extractionFlag: yes
04:03:45 - e5689845 - Info     update_t_AggPrjData: no
04:03:45 - e5689845 - Info     update_t_IMPData: no
04:03:45 - e5689845 - Info     update_t_PRJData: yes
04:03:45 - e5689845 - Info     update_t_StoMData: no
04:03:45 - e5689845 - Info     update_t_StoYData: no
04:03:45 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
04:03:45 - e5689845 - Info     variable_filter: ''
04:03:45 - e5689845 - Step InfoCreating new load request
04:03:45 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
04:03:45 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T04:03:45",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
        }
    }
}
04:03:48 - e5689845 - Info     <Response [201]>
04:03:48 - e5689845 - Step Infoconnecting to postgres control database
04:03:48 - e5689845 - Info     connected to controldb_idr
04:03:53 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
04:03:53 - e5689845 - Info     requestid: 98
04:03:53 - e5689845 - Info     requestdescription: csvTest_RP202501_Step2
04:03:53 - e5689845 - Info     request (98) in progress (status 3)
04:03:53 - e5689845 - Info     wait 10s for next status check
04:04:03 - e5689845 - Info     request (98) in progress (status 4)
04:04:03 - e5689845 - Info     wait 10s for next status check
04:04:13 - e5689845 - Info     request (98) in progress (status 4)
04:04:13 - e5689845 - Info     wait 10s for next status check
04:04:23 - e5689845 - Info     request (98) in progress (status 4)
04:04:23 - e5689845 - Info     wait 10s for next status check
04:04:33 - e5689845 - Info     request (98) in progress (status 4)
04:04:33 - e5689845 - Info     wait 10s for next status check
04:04:43 - e5689845 - Info     request (98) in progress (status 4)
04:04:43 - e5689845 - Info     wait 10s for next status check
04:04:53 - e5689845 - Info     request (98) in progress (status 4)
04:04:53 - e5689845 - Info     wait 10s for next status check
04:05:03 - e5689845 - Info     request (98) in progress (status 4)
04:05:03 - e5689845 - Info     wait 10s for next status check
04:05:13 - e5689845 - Info     request (98) in progress (status 4)
04:05:13 - e5689845 - Info     wait 10s for next status check
04:05:23 - e5689845 - Info     request (98) in progress (status 4)
04:05:23 - e5689845 - Info     wait 10s for next status check
04:05:33 - e5689845 - Info     request (98) in progress (status 4)
04:05:33 - e5689845 - Info     wait 10s for next status check
04:05:43 - e5689845 - Info     request (98) in progress (status 4)
04:05:43 - e5689845 - Info     wait 10s for next status check
04:05:53 - e5689845 - Info     request (98) in progress (status 4)
04:05:53 - e5689845 - Info     wait 10s for next status check
04:06:03 - e5689845 - Info     request (98) in progress (status 4)
04:06:03 - e5689845 - Info     wait 10s for next status check
04:06:13 - e5689845 - Info     request (98) in progress (status 4)
04:06:13 - e5689845 - Info     wait 10s for next status check
04:06:23 - e5689845 - Info     request (98) in progress (status 4)
04:06:23 - e5689845 - Info     wait 10s for next status check
04:06:33 - e5689845 - Info     request (98) in progress (status 4)
04:06:33 - e5689845 - Info     wait 10s for next status check
04:06:43 - e5689845 - Info     request (98) in progress (status 4)
04:06:43 - e5689845 - Info     wait 10s for next status check
04:06:53 - e5689845 - Info     request (98) in progress (status 4)
04:06:53 - e5689845 - Info     wait 10s for next status check
04:07:03 - e5689845 - Info     request (98) in progress (status 4)
04:07:03 - e5689845 - Info     wait 10s for next status check
04:07:14 - e5689845 - Info     request (98) in progress (status 4)
04:07:14 - e5689845 - Info     wait 10s for next status check
04:07:24 - e5689845 - Info     request (98) in progress (status 4)
04:07:24 - e5689845 - Info     wait 10s for next status check
04:07:34 - e5689845 - Info     request (98) in progress (status 4)
04:07:34 - e5689845 - Info     wait 10s for next status check
04:07:44 - e5689845 - Info     request (98) in progress (status 4)
04:07:44 - e5689845 - Info     wait 10s for next status check
04:07:54 - e5689845 - Info     request (98) in progress (status 4)
04:07:54 - e5689845 - Info     wait 10s for next status check
04:08:04 - e5689845 - Info     request (98) in progress (status 4)
04:08:04 - e5689845 - Info     wait 10s for next status check
04:08:14 - e5689845 - Info     request (98) in progress (status 4)
04:08:14 - e5689845 - Info     wait 10s for next status check
04:08:24 - e5689845 - Info     request (98) in progress (status 4)
04:08:24 - e5689845 - Info     wait 10s for next status check
04:08:34 - e5689845 - Info     request (98) in progress (status 4)
04:08:34 - e5689845 - Info     wait 10s for next status check
04:08:44 - e5689845 - Info     request (98) in progress (status 4)
04:08:44 - e5689845 - Info     wait 10s for next status check
04:08:54 - e5689845 - Info     request (98) in progress (status 4)
04:08:54 - e5689845 - Info     wait 10s for next status check
04:09:04 - e5689845 - Info     request (98) in progress (status 4)
04:09:04 - e5689845 - Info     wait 10s for next status check
04:09:14 - e5689845 - Info     request (98) in progress (status 4)
04:09:14 - e5689845 - Info     wait 10s for next status check
04:09:24 - e5689845 - Info     request (98) in progress (status 4)
04:09:24 - e5689845 - Info     wait 10s for next status check
04:09:34 - e5689845 - Info     request (98) in progress (status 4)
04:09:34 - e5689845 - Info     wait 10s for next status check
04:09:44 - e5689845 - Info     request (98) in progress (status 4)
04:09:44 - e5689845 - Info     wait 10s for next status check
04:09:54 - e5689845 - Info     request (98) in progress (status 4)
04:09:54 - e5689845 - Info     wait 10s for next status check
04:10:04 - e5689845 - Info     request (98) in progress (status 4)
04:10:04 - e5689845 - Info     wait 10s for next status check
04:10:14 - e5689845 - Info     request (98) in progress (status 4)
04:10:14 - e5689845 - Info     wait 10s for next status check
04:10:24 - e5689845 - Info     request (98) in progress (status 4)
04:10:24 - e5689845 - Info     wait 10s for next status check
04:10:34 - e5689845 - Info     request (98) in progress (status 4)
04:10:34 - e5689845 - Info     wait 10s for next status check
04:10:44 - e5689845 - Info     request (98) in progress (status 4)
04:10:44 - e5689845 - Info     wait 10s for next status check
04:10:54 - e5689845 - Info     request (98) in progress (status 4)
04:10:54 - e5689845 - Info     wait 10s for next status check
04:11:04 - e5689845 - Info     request (98) in progress (status 4)
04:11:04 - e5689845 - Info     wait 10s for next status check
04:11:14 - e5689845 - Info     request (98) in progress (status 4)
04:11:14 - e5689845 - Info     wait 10s for next status check
04:11:24 - e5689845 - Info     request (98) in progress (status 4)
04:11:24 - e5689845 - Info     wait 10s for next status check
04:11:34 - e5689845 - Info     request (98) in progress (status 4)
04:11:34 - e5689845 - Info     wait 10s for next status check
04:11:44 - e5689845 - Info     request (98) in progress (status 4)
04:11:44 - e5689845 - Info     wait 10s for next status check
04:11:54 - e5689845 - Info     request (98) in progress (status 4)
04:11:54 - e5689845 - Info     wait 10s for next status check
04:12:04 - e5689845 - Info     request (98) in progress (status 4)
04:12:04 - e5689845 - Info     wait 10s for next status check
04:12:14 - e5689845 - Info     request (98) in progress (status 5)
04:12:14 - e5689845 - Success  request completed
04:12:14 - e5689845 - Step Infoconnecting to postgres results database
04:12:14 - e5689845 - Info     connected to resultsdb_idr
04:12:14 - e5689845 - Info     updating t_PRJData
04:12:14 - e5689845 - Info     CALL "Analytics".spLoad_t_PRJData(98,''::text)
04:12:54 - e5689845 - Info     NOTICE:  Deleted 0 rows from t_PRJData for resultslocation=\\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results and requestdescription=csvTest_RP202501_Step2

04:12:54 - e5689845 - Info     NOTICE:  Loaded 183160 rows into t_PRJData for requestid=98 

04:12:54 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["Ref*.csv"]
            }
04:12:54 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
04:12:54 - e5689845 - Step InfoCreating new csv load request
04:12:54 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
04:12:54 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-10T04:12:54",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["Ref*.csv"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
04:12:54 - e5689845 - Info     <Response [201]>