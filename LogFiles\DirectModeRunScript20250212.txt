
04:53:22 - e5689845 - Step Inforeading results settings
04:53:22 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
04:53:22 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
04:53:22 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
04:53:22 - e5689845 - Info     resultsLoadingFlag: no
04:53:22 - e5689845 - Info     csvLoadingFlag: yes
04:53:22 - e5689845 - Info     extractionFlag: yes
04:53:22 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["C_CPA_.rpt"]
            }
04:53:22 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
04:53:22 - e5689845 - Step InfoCreating new csv load request
04:53:22 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
04:53:22 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-12T04:53:22",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["C_CPA_.rpt"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
04:53:25 - e5689845 - Info     <Response [201]>
04:55:57 - e5689845 - Step Inforeading results settings
04:55:57 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
04:55:57 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
04:55:57 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
04:55:57 - e5689845 - Info     resultsLoadingFlag: no
04:55:57 - e5689845 - Info     csvLoadingFlag: yes
04:55:57 - e5689845 - Info     extractionFlag: yes
04:55:57 - e5689845 - Info     CsvRequestFilters: "requestFilters": {
            "fileNames": ["C_CPA_.rpt"]
            }
04:55:57 - e5689845 - Info     CsvParameters: "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
04:55:57 - e5689845 - Step InfoCreating new csv load request
04:55:57 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
04:55:57 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "csvTest_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\Sources",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "Prophet",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-12T04:55:57",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": ["C_CPA_.rpt"]
            },
            "requestParameters":{"SubType":"CSV","ListSeperator":",","NumberSeperator":".","Quote":"\""}
        }
    }
}
04:55:57 - e5689845 - Info     <Response [201]>
07:01:22 - e5689845 - Step Inforeading results settings
07:01:22 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:01:22 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
07:01:22 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:01:22 - e5689845 - Info     resultsLoadingFlag: no
07:01:22 - e5689845 - Info     csvLoadingFlag: no
07:01:22 - e5689845 - Info     extractionFlag: yes
07:01:22 - e5689845 - Step InfoGenerating extraction
07:01:22 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\"Analytics".vBI_PRJData.csv
07:01:22 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJDatawhere reuestdescription = ALS_RP202501_Step1
07:01:22 - e5689845 - Error    Error: Boolean value of this clause is not defined
07:02:42 - e5689845 - Step Inforeading results settings
07:02:42 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:02:42 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
07:02:42 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:02:42 - e5689845 - Info     resultsLoadingFlag: no
07:02:42 - e5689845 - Info     csvLoadingFlag: no
07:02:42 - e5689845 - Info     extractionFlag: yes
07:02:42 - e5689845 - Step InfoGenerating extraction
07:02:42 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\"Analytics".vBI_PRJData.csv
07:02:42 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where reuestdescription = 'ALS_RP202501_Step1'
07:02:42 - e5689845 - Error    Error: Boolean value of this clause is not defined
07:03:21 - e5689845 - Step Inforeading results settings
07:03:21 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:03:21 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
07:03:21 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:03:21 - e5689845 - Info     resultsLoadingFlag: no
07:03:21 - e5689845 - Info     csvLoadingFlag: no
07:03:21 - e5689845 - Info     extractionFlag: yes
07:03:21 - e5689845 - Step InfoGenerating extraction
07:03:21 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\report.csv
07:03:21 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where reuestdescription = 'ALS_RP202501_Step1'
07:03:21 - e5689845 - Error    Error: argument 1 must be a string or unicode object: got TextClause instead
07:04:33 - e5689845 - Step Inforeading results settings
07:04:33 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:04:33 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
07:04:34 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:04:34 - e5689845 - Info     resultsLoadingFlag: no
07:04:34 - e5689845 - Info     csvLoadingFlag: no
07:04:34 - e5689845 - Info     extractionFlag: yes
07:04:34 - e5689845 - Step InfoGenerating extraction
07:04:34 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\report.csv
07:04:34 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where reuestdescription = 'ALS_RP202501_Step1'
07:04:34 - e5689845 - Error    Error: argument 1 must be a string or unicode object: got TextClause instead
07:05:32 - e5689845 - Step Inforeading results settings
07:05:32 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:05:32 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
07:05:32 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:05:32 - e5689845 - Info     resultsLoadingFlag: no
07:05:32 - e5689845 - Info     csvLoadingFlag: no
07:05:32 - e5689845 - Info     extractionFlag: yes
07:05:32 - e5689845 - Step InfoGenerating extraction
07:05:32 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\report.csv
07:05:32 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where reuestdescription = 'ALS_RP202501_Step1'
07:05:32 - e5689845 - Error    Error: column "reuestdescription" does not exist
LINE 1: select * from "Analytics".vBI_PRJData where reuestdescriptio...
                                                    ^
HINT:  Perhaps you meant to reference the column "vbi_prjdata.requestdescription".

07:05:59 - e5689845 - Step Inforeading results settings
07:05:59 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:05:59 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
07:05:59 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:05:59 - e5689845 - Info     resultsLoadingFlag: no
07:05:59 - e5689845 - Info     csvLoadingFlag: no
07:05:59 - e5689845 - Info     extractionFlag: yes
07:05:59 - e5689845 - Step InfoGenerating extraction
07:05:59 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\report.csv
07:05:59 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where requestdescription = 'ALS_RP202501_Step1'
07:06:05 - e5689845 - Error    Error: name 'csv' is not defined
07:07:11 - e5689845 - Step Inforeading results settings
07:07:11 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:07:11 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
07:07:11 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:07:11 - e5689845 - Info     resultsLoadingFlag: no
07:07:11 - e5689845 - Info     csvLoadingFlag: no
07:07:11 - e5689845 - Info     extractionFlag: yes
07:07:11 - e5689845 - Step InfoGenerating extraction
07:07:11 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\report.csv
07:07:11 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where requestdescription = 'ALS_RP202501_Step1'
07:13:36 - e5689845 - Step Inforeading results settings
07:13:37 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:13:37 - e5689845 - Info     requestDescription: csvTest_RP202501_Step2
07:13:37 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:13:37 - e5689845 - Info     resultsLoadingFlag: no
07:13:37 - e5689845 - Info     csvLoadingFlag: no
07:13:37 - e5689845 - Info     extractionFlag: yes
07:13:37 - e5689845 - Step InfoGenerating extraction
07:13:37 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\report.csv
07:13:37 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where requestdescription = 'PRJ load'
07:27:20 - e5689845 - Step Inforeading results settings
07:27:20 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:27:20 - e5689845 - Info     requestDescription: ALS_RP202501_Step2
07:27:20 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:27:20 - e5689845 - Info     resultsLoadingFlag: yes
07:27:20 - e5689845 - Info     csvLoadingFlag: no
07:27:20 - e5689845 - Info     extractionFlag: yes
07:27:20 - e5689845 - Info     update_t_AggPrjData: no
07:27:20 - e5689845 - Info     update_t_IMPData: no
07:27:20 - e5689845 - Info     update_t_PRJData: yes
07:27:20 - e5689845 - Info     update_t_StoMData: no
07:27:20 - e5689845 - Info     update_t_StoYData: no
07:27:20 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
07:27:20 - e5689845 - Info     variable_filter: ''
07:27:20 - e5689845 - Step InfoCreating new load request
07:27:20 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
07:27:20 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "ALS_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-12T07:27:20",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
        }
    }
}
07:27:23 - e5689845 - Info     <Response [201]>
07:27:23 - e5689845 - Step Infoconnecting to postgres control database
07:27:23 - e5689845 - Info     connected to controldb_idr
07:27:28 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
07:27:28 - e5689845 - Info     requestid: 102
07:27:28 - e5689845 - Info     requestdescription: ALS_RP202501_Step2
07:27:28 - e5689845 - Info     request (102) in progress (status 3)
07:27:28 - e5689845 - Info     wait 10s for next status check
07:27:38 - e5689845 - Info     request (102) in progress (status 4)
07:27:38 - e5689845 - Info     wait 10s for next status check
07:27:48 - e5689845 - Info     request (102) in progress (status 4)
07:27:48 - e5689845 - Info     wait 10s for next status check
07:27:58 - e5689845 - Info     request (102) in progress (status 4)
07:27:58 - e5689845 - Info     wait 10s for next status check
07:28:08 - e5689845 - Info     request (102) in progress (status 4)
07:28:08 - e5689845 - Info     wait 10s for next status check
07:28:18 - e5689845 - Info     request (102) in progress (status 4)
07:28:18 - e5689845 - Info     wait 10s for next status check
07:28:28 - e5689845 - Info     request (102) in progress (status 4)
07:28:28 - e5689845 - Info     wait 10s for next status check
07:28:38 - e5689845 - Info     request (102) in progress (status 4)
07:28:38 - e5689845 - Info     wait 10s for next status check
07:28:48 - e5689845 - Info     request (102) in progress (status 4)
07:28:48 - e5689845 - Info     wait 10s for next status check
07:28:58 - e5689845 - Info     request (102) in progress (status 4)
07:28:58 - e5689845 - Info     wait 10s for next status check
07:29:08 - e5689845 - Info     request (102) in progress (status 4)
07:29:08 - e5689845 - Info     wait 10s for next status check
07:29:18 - e5689845 - Info     request (102) in progress (status 4)
07:29:18 - e5689845 - Info     wait 10s for next status check
07:29:28 - e5689845 - Info     request (102) in progress (status 4)
07:29:28 - e5689845 - Info     wait 10s for next status check
07:29:38 - e5689845 - Info     request (102) in progress (status 4)
07:29:38 - e5689845 - Info     wait 10s for next status check
07:29:48 - e5689845 - Info     request (102) in progress (status 4)
07:29:48 - e5689845 - Info     wait 10s for next status check
07:29:58 - e5689845 - Info     request (102) in progress (status 4)
07:29:58 - e5689845 - Info     wait 10s for next status check
07:30:08 - e5689845 - Info     request (102) in progress (status 4)
07:30:08 - e5689845 - Info     wait 10s for next status check
07:30:18 - e5689845 - Info     request (102) in progress (status 4)
07:30:18 - e5689845 - Info     wait 10s for next status check
07:30:28 - e5689845 - Info     request (102) in progress (status 4)
07:30:28 - e5689845 - Info     wait 10s for next status check
07:30:38 - e5689845 - Info     request (102) in progress (status 4)
07:30:38 - e5689845 - Info     wait 10s for next status check
07:30:49 - e5689845 - Info     request (102) in progress (status 4)
07:30:49 - e5689845 - Info     wait 10s for next status check
07:30:59 - e5689845 - Info     request (102) in progress (status 4)
07:30:59 - e5689845 - Info     wait 10s for next status check
07:31:09 - e5689845 - Info     request (102) in progress (status 4)
07:31:09 - e5689845 - Info     wait 10s for next status check
07:31:19 - e5689845 - Info     request (102) in progress (status 4)
07:31:19 - e5689845 - Info     wait 10s for next status check
07:31:29 - e5689845 - Info     request (102) in progress (status 4)
07:31:29 - e5689845 - Info     wait 10s for next status check
07:31:39 - e5689845 - Info     request (102) in progress (status 4)
07:31:39 - e5689845 - Info     wait 10s for next status check
07:31:49 - e5689845 - Info     request (102) in progress (status 4)
07:31:49 - e5689845 - Info     wait 10s for next status check
07:31:59 - e5689845 - Info     request (102) in progress (status 4)
07:31:59 - e5689845 - Info     wait 10s for next status check
07:32:09 - e5689845 - Info     request (102) in progress (status 4)
07:32:09 - e5689845 - Info     wait 10s for next status check
07:32:19 - e5689845 - Info     request (102) in progress (status 4)
07:32:19 - e5689845 - Info     wait 10s for next status check
07:32:29 - e5689845 - Info     request (102) in progress (status 4)
07:32:29 - e5689845 - Info     wait 10s for next status check
07:32:39 - e5689845 - Info     request (102) in progress (status 4)
07:32:39 - e5689845 - Info     wait 10s for next status check
07:32:49 - e5689845 - Info     request (102) in progress (status 4)
07:32:49 - e5689845 - Info     wait 10s for next status check
07:32:59 - e5689845 - Info     request (102) in progress (status 6)
07:32:59 - e5689845 - Error    request failed, please check db log for more information
07:34:11 - e5689845 - Step Inforeading results settings
07:34:11 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
07:34:11 - e5689845 - Info     requestDescription: ALS_RP202501_Step2
07:34:11 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
07:34:11 - e5689845 - Info     resultsLoadingFlag: yes
07:34:11 - e5689845 - Info     csvLoadingFlag: no
07:34:11 - e5689845 - Info     extractionFlag: yes
07:34:11 - e5689845 - Info     update_t_AggPrjData: no
07:34:11 - e5689845 - Info     update_t_IMPData: no
07:34:11 - e5689845 - Info     update_t_PRJData: yes
07:34:11 - e5689845 - Info     update_t_StoMData: no
07:34:11 - e5689845 - Info     update_t_StoYData: no
07:34:11 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
07:34:11 - e5689845 - Info     variable_filter: 'AND r2_1.value::text in (''ACCRD_RB_IF'', ''ACCRD_RB_IF_FL(1)'', ''ADD_PROFIT'', ''ADD_PROFIT_FL(1)'', ''ADD_TAX'', ''ADD_TAX_FL(1)'', ''ADD_TRANSFER'', ''ADD_TRANSFER_FL(1)'')'
07:34:11 - e5689845 - Step InfoCreating new load request
07:34:11 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
07:34:11 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "ALS_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-12T07:34:11",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
        }
    }
}
07:34:11 - e5689845 - Info     <Response [201]>
07:34:11 - e5689845 - Step Infoconnecting to postgres control database
07:34:11 - e5689845 - Info     connected to controldb_idr
07:34:16 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
07:34:16 - e5689845 - Info     requestid: 103
07:34:16 - e5689845 - Info     requestdescription: ALS_RP202501_Step2
07:34:16 - e5689845 - Info     request (103) in progress (status 4)
07:34:16 - e5689845 - Info     wait 10s for next status check
07:34:26 - e5689845 - Info     request (103) in progress (status 4)
07:34:26 - e5689845 - Info     wait 10s for next status check
07:34:36 - e5689845 - Info     request (103) in progress (status 4)
07:34:36 - e5689845 - Info     wait 10s for next status check
07:34:46 - e5689845 - Info     request (103) in progress (status 4)
07:34:46 - e5689845 - Info     wait 10s for next status check
07:34:56 - e5689845 - Info     request (103) in progress (status 4)
07:34:56 - e5689845 - Info     wait 10s for next status check
07:35:06 - e5689845 - Info     request (103) in progress (status 4)
07:35:07 - e5689845 - Info     wait 10s for next status check
07:35:17 - e5689845 - Info     request (103) in progress (status 4)
07:35:17 - e5689845 - Info     wait 10s for next status check
07:35:27 - e5689845 - Info     request (103) in progress (status 4)
07:35:27 - e5689845 - Info     wait 10s for next status check
07:35:37 - e5689845 - Info     request (103) in progress (status 4)
07:35:37 - e5689845 - Info     wait 10s for next status check
07:35:47 - e5689845 - Info     request (103) in progress (status 4)
07:35:47 - e5689845 - Info     wait 10s for next status check
07:35:57 - e5689845 - Info     request (103) in progress (status 4)
07:35:57 - e5689845 - Info     wait 10s for next status check
07:36:07 - e5689845 - Info     request (103) in progress (status 4)
07:36:07 - e5689845 - Info     wait 10s for next status check
07:36:17 - e5689845 - Info     request (103) in progress (status 4)
07:36:17 - e5689845 - Info     wait 10s for next status check
07:36:27 - e5689845 - Info     request (103) in progress (status 4)
07:36:27 - e5689845 - Info     wait 10s for next status check
07:36:37 - e5689845 - Info     request (103) in progress (status 4)
07:36:37 - e5689845 - Info     wait 10s for next status check
07:36:47 - e5689845 - Info     request (103) in progress (status 4)
07:36:47 - e5689845 - Info     wait 10s for next status check
07:36:57 - e5689845 - Info     request (103) in progress (status 4)
07:36:57 - e5689845 - Info     wait 10s for next status check
07:37:07 - e5689845 - Info     request (103) in progress (status 4)
07:37:07 - e5689845 - Info     wait 10s for next status check
07:37:17 - e5689845 - Info     request (103) in progress (status 4)
07:37:17 - e5689845 - Info     wait 10s for next status check
07:37:27 - e5689845 - Info     request (103) in progress (status 4)
07:37:27 - e5689845 - Info     wait 10s for next status check
07:37:37 - e5689845 - Info     request (103) in progress (status 4)
07:37:37 - e5689845 - Info     wait 10s for next status check
07:37:47 - e5689845 - Info     request (103) in progress (status 4)
07:37:47 - e5689845 - Info     wait 10s for next status check
07:37:57 - e5689845 - Info     request (103) in progress (status 4)
07:37:57 - e5689845 - Info     wait 10s for next status check
07:38:07 - e5689845 - Info     request (103) in progress (status 4)
07:38:07 - e5689845 - Info     wait 10s for next status check
07:38:17 - e5689845 - Info     request (103) in progress (status 4)
07:38:17 - e5689845 - Info     wait 10s for next status check
07:38:27 - e5689845 - Info     request (103) in progress (status 4)
07:38:27 - e5689845 - Info     wait 10s for next status check
07:38:37 - e5689845 - Info     request (103) in progress (status 4)
07:38:37 - e5689845 - Info     wait 10s for next status check
07:38:47 - e5689845 - Info     request (103) in progress (status 4)
07:38:47 - e5689845 - Info     wait 10s for next status check
07:38:57 - e5689845 - Info     request (103) in progress (status 4)
07:38:57 - e5689845 - Info     wait 10s for next status check
07:39:07 - e5689845 - Info     request (103) in progress (status 4)
07:39:07 - e5689845 - Info     wait 10s for next status check
07:39:17 - e5689845 - Info     request (103) in progress (status 4)
07:39:17 - e5689845 - Info     wait 10s for next status check
07:39:27 - e5689845 - Info     request (103) in progress (status 4)
07:39:27 - e5689845 - Info     wait 10s for next status check
07:39:37 - e5689845 - Info     request (103) in progress (status 4)
07:39:37 - e5689845 - Info     wait 10s for next status check
07:39:47 - e5689845 - Info     request (103) in progress (status 4)
07:39:47 - e5689845 - Info     wait 10s for next status check
07:39:57 - e5689845 - Info     request (103) in progress (status 5)
07:39:57 - e5689845 - Success  request completed
07:39:57 - e5689845 - Step Infoconnecting to postgres results database
07:39:57 - e5689845 - Info     connected to resultsdb_idr
07:39:57 - e5689845 - Info     updating t_PRJData
07:39:57 - e5689845 - Info     CALL "Analytics".spLoad_t_PRJData(103,'AND r2_1.value::text in (''ACCRD_RB_IF'', ''ACCRD_RB_IF_FL(1)'', ''ADD_PROFIT'', ''ADD_PROFIT_FL(1)'', ''ADD_TAX'', ''ADD_TAX_FL(1)'', ''ADD_TRANSFER'', ''ADD_TRANSFER_FL(1)'')'::text)
07:40:01 - e5689845 - Info     NOTICE:  Deleted 0 rows from t_PRJData for resultslocation=\\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results and requestdescription=ALS_RP202501_Step2

07:40:01 - e5689845 - Info     NOTICE:  Loaded 1928 rows into t_PRJData for requestid=103 

07:40:01 - e5689845 - Step InfoGenerating extraction
07:40:01 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\report.csv
07:40:01 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where requestdescription = 'PRJ load'