
08:27:44 - e5689845 - Step Inforeading results settings
08:27:44 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
08:27:44 - e5689845 - Info     requestDescription: ALS_RP202501_Step2
08:27:44 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
08:27:44 - e5689845 - Info     resultsLoadingFlag: yes
08:27:44 - e5689845 - Info     csvLoadingFlag: no
08:27:44 - e5689845 - Info     extractionFlag: yes
08:27:44 - e5689845 - Info     update_t_AggPrjData: no
08:27:44 - e5689845 - Info     update_t_IMPData: no
08:27:44 - e5689845 - Info     update_t_PRJData: yes
08:27:44 - e5689845 - Info     update_t_StoMData: no
08:27:44 - e5689845 - Info     update_t_StoYData: no
08:27:44 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
08:27:44 - e5689845 - Info     variable_filter: 'AND r2_1.value::text in (''ACCRD_RB_IF'', ''ACCRD_RB_IF_FL(1)'', ''ADD_PROFIT'', ''ADD_PROFIT_FL(1)'', ''ADD_TAX'', ''ADD_TAX_FL(1)'', ''ADD_TRANSFER'', ''ADD_TRANSFER_FL(1)'')'
08:27:44 - e5689845 - Step InfoCreating new load request
08:27:44 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
08:27:44 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "ALS_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-20T08:27:44",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
        }
    }
}
08:27:47 - e5689845 - Info     <Response [201]>
08:27:47 - e5689845 - Step Infoconnecting to postgres control database
08:27:47 - e5689845 - Info     connected to controldb_idr
08:29:18 - e5689845 - Step Inforeading results settings
08:29:18 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results
08:29:18 - e5689845 - Info     requestDescription: ALS_RP202501_Step2
08:29:18 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step2.py
08:29:19 - e5689845 - Info     resultsLoadingFlag: yes
08:29:19 - e5689845 - Info     csvLoadingFlag: no
08:29:19 - e5689845 - Info     extractionFlag: yes
08:29:19 - e5689845 - Info     update_t_AggPrjData: no
08:29:19 - e5689845 - Info     update_t_IMPData: no
08:29:19 - e5689845 - Info     update_t_PRJData: yes
08:29:19 - e5689845 - Info     update_t_StoMData: no
08:29:19 - e5689845 - Info     update_t_StoYData: no
08:29:19 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
08:29:19 - e5689845 - Info     variable_filter: 'AND r2_1.value::text in (''ACCRD_RB_IF'', ''ACCRD_RB_IF_FL(1)'', ''ADD_PROFIT'', ''ADD_PROFIT_FL(1)'', ''ADD_TAX'', ''ADD_TAX_FL(1)'', ''ADD_TRANSFER'', ''ADD_TRANSFER_FL(1)'')'
08:29:19 - e5689845 - Step InfoCreating new load request
08:29:19 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
08:29:19 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "ALS_RP202501_Step2",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-02-20T08:29:19",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [120],
            "products": ["A_QFUND"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1],
            "monthFirst": 1,
            "monthTimes": 12
            }
        }
    }
}
08:29:19 - e5689845 - Info     <Response [201]>
08:29:19 - e5689845 - Step Infoconnecting to postgres control database
08:29:19 - e5689845 - Info     connected to controldb_idr
08:29:24 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
08:29:24 - e5689845 - Info     requestid: 105
08:29:24 - e5689845 - Info     requestdescription: ALS_RP202501_Step2
08:29:24 - e5689845 - Info     request (105) in progress (status 4)
08:29:24 - e5689845 - Info     wait 10s for next status check
08:29:34 - e5689845 - Info     request (105) in progress (status 4)
08:29:34 - e5689845 - Info     wait 10s for next status check
08:29:44 - e5689845 - Info     request (105) in progress (status 4)
08:29:44 - e5689845 - Info     wait 10s for next status check
08:29:54 - e5689845 - Info     request (105) in progress (status 4)
08:29:54 - e5689845 - Info     wait 10s for next status check
08:30:04 - e5689845 - Info     request (105) in progress (status 4)
08:30:04 - e5689845 - Info     wait 10s for next status check
08:30:14 - e5689845 - Info     request (105) in progress (status 4)
08:30:14 - e5689845 - Info     wait 10s for next status check
08:30:24 - e5689845 - Info     request (105) in progress (status 4)
08:30:24 - e5689845 - Info     wait 10s for next status check
08:30:34 - e5689845 - Info     request (105) in progress (status 4)
08:30:34 - e5689845 - Info     wait 10s for next status check
08:30:44 - e5689845 - Info     request (105) in progress (status 4)
08:30:44 - e5689845 - Info     wait 10s for next status check
08:30:54 - e5689845 - Info     request (105) in progress (status 4)
08:30:54 - e5689845 - Info     wait 10s for next status check
08:31:04 - e5689845 - Info     request (105) in progress (status 4)
08:31:04 - e5689845 - Info     wait 10s for next status check
08:31:14 - e5689845 - Info     request (105) in progress (status 4)
08:31:14 - e5689845 - Info     wait 10s for next status check
08:31:24 - e5689845 - Info     request (105) in progress (status 4)
08:31:24 - e5689845 - Info     wait 10s for next status check
08:31:34 - e5689845 - Info     request (105) in progress (status 4)
08:31:34 - e5689845 - Info     wait 10s for next status check
08:31:44 - e5689845 - Info     request (105) in progress (status 4)
08:31:44 - e5689845 - Info     wait 10s for next status check
08:31:54 - e5689845 - Info     request (105) in progress (status 4)
08:31:54 - e5689845 - Info     wait 10s for next status check
08:32:04 - e5689845 - Info     request (105) in progress (status 4)
08:32:04 - e5689845 - Info     wait 10s for next status check
08:32:14 - e5689845 - Info     request (105) in progress (status 4)
08:32:14 - e5689845 - Info     wait 10s for next status check
08:32:24 - e5689845 - Info     request (105) in progress (status 4)
08:32:24 - e5689845 - Info     wait 10s for next status check
08:32:34 - e5689845 - Info     request (105) in progress (status 4)
08:32:34 - e5689845 - Info     wait 10s for next status check
08:32:44 - e5689845 - Info     request (105) in progress (status 4)
08:32:44 - e5689845 - Info     wait 10s for next status check
08:32:54 - e5689845 - Info     request (105) in progress (status 4)
08:32:54 - e5689845 - Info     wait 10s for next status check
08:33:04 - e5689845 - Info     request (105) in progress (status 4)
08:33:04 - e5689845 - Info     wait 10s for next status check
08:33:14 - e5689845 - Info     request (105) in progress (status 4)
08:33:14 - e5689845 - Info     wait 10s for next status check
08:33:24 - e5689845 - Info     request (105) in progress (status 4)
08:33:24 - e5689845 - Info     wait 10s for next status check
08:33:34 - e5689845 - Info     request (105) in progress (status 4)
08:33:34 - e5689845 - Info     wait 10s for next status check
08:33:44 - e5689845 - Info     request (105) in progress (status 4)
08:33:44 - e5689845 - Info     wait 10s for next status check
08:33:54 - e5689845 - Info     request (105) in progress (status 4)
08:33:54 - e5689845 - Info     wait 10s for next status check
08:34:04 - e5689845 - Info     request (105) in progress (status 4)
08:34:04 - e5689845 - Info     wait 10s for next status check
08:34:14 - e5689845 - Info     request (105) in progress (status 4)
08:34:14 - e5689845 - Info     wait 10s for next status check
08:34:24 - e5689845 - Info     request (105) in progress (status 4)
08:34:24 - e5689845 - Info     wait 10s for next status check
08:34:34 - e5689845 - Info     request (105) in progress (status 4)
08:34:34 - e5689845 - Info     wait 10s for next status check
08:34:45 - e5689845 - Info     request (105) in progress (status 4)
08:34:45 - e5689845 - Info     wait 10s for next status check
08:34:55 - e5689845 - Info     request (105) in progress (status 4)
08:34:55 - e5689845 - Info     wait 10s for next status check
08:35:05 - e5689845 - Info     request (105) in progress (status 4)
08:35:05 - e5689845 - Info     wait 10s for next status check
08:35:15 - e5689845 - Info     request (105) in progress (status 4)
08:35:15 - e5689845 - Info     wait 10s for next status check
08:35:25 - e5689845 - Info     request (105) in progress (status 4)
08:35:25 - e5689845 - Info     wait 10s for next status check
08:35:35 - e5689845 - Info     request (105) in progress (status 4)
08:35:35 - e5689845 - Info     wait 10s for next status check
08:35:45 - e5689845 - Info     request (105) in progress (status 4)
08:35:45 - e5689845 - Info     wait 10s for next status check
08:35:55 - e5689845 - Info     request (105) in progress (status 4)
08:35:55 - e5689845 - Info     wait 10s for next status check
08:36:05 - e5689845 - Info     request (105) in progress (status 4)
08:36:05 - e5689845 - Info     wait 10s for next status check
08:36:15 - e5689845 - Info     request (105) in progress (status 4)
08:36:15 - e5689845 - Info     wait 10s for next status check
08:36:25 - e5689845 - Info     request (105) in progress (status 4)
08:36:25 - e5689845 - Info     wait 10s for next status check
08:36:35 - e5689845 - Info     request (105) in progress (status 4)
08:36:35 - e5689845 - Info     wait 10s for next status check
08:36:45 - e5689845 - Info     request (105) in progress (status 4)
08:36:45 - e5689845 - Info     wait 10s for next status check
08:36:55 - e5689845 - Info     request (105) in progress (status 4)
08:36:55 - e5689845 - Info     wait 10s for next status check
08:37:05 - e5689845 - Info     request (105) in progress (status 4)
08:37:05 - e5689845 - Info     wait 10s for next status check
08:37:15 - e5689845 - Info     request (105) in progress (status 4)
08:37:15 - e5689845 - Info     wait 10s for next status check
08:37:25 - e5689845 - Info     request (105) in progress (status 4)
08:37:25 - e5689845 - Info     wait 10s for next status check
08:37:35 - e5689845 - Info     request (105) in progress (status 4)
08:37:35 - e5689845 - Info     wait 10s for next status check
08:37:45 - e5689845 - Info     request (105) in progress (status 4)
08:37:45 - e5689845 - Info     wait 10s for next status check
08:37:55 - e5689845 - Info     request (105) in progress (status 4)
08:37:55 - e5689845 - Info     wait 10s for next status check
08:38:05 - e5689845 - Info     request (105) in progress (status 4)
08:38:05 - e5689845 - Info     wait 10s for next status check
08:38:15 - e5689845 - Info     request (105) in progress (status 4)
08:38:15 - e5689845 - Info     wait 10s for next status check
08:38:25 - e5689845 - Info     request (105) in progress (status 5)
08:38:25 - e5689845 - Success  request completed
08:38:25 - e5689845 - Step Infoconnecting to postgres results database
08:38:25 - e5689845 - Info     connected to resultsdb_idr
08:38:25 - e5689845 - Info     updating t_PRJData
08:38:25 - e5689845 - Info     CALL "Analytics".spLoad_t_PRJData(105,'AND r2_1.value::text in (''ACCRD_RB_IF'', ''ACCRD_RB_IF_FL(1)'', ''ADD_PROFIT'', ''ADD_PROFIT_FL(1)'', ''ADD_TAX'', ''ADD_TAX_FL(1)'', ''ADD_TRANSFER'', ''ADD_TRANSFER_FL(1)'')'::text)
08:38:33 - e5689845 - Info     NOTICE:  Deleted 1928 rows from t_PRJData for resultslocation=\\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\results and requestdescription=ALS_RP202501_Step2

08:38:33 - e5689845 - Info     NOTICE:  Loaded 1928 rows into t_PRJData for requestid=105 

08:38:33 - e5689845 - Step InfoGenerating extraction
08:38:33 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\ALS_202501.csv.csv
08:38:33 - e5689845 - Info     extract_query: select * from "Analytics".vbi_prjdata where requestdescription = 'ALS_RP202501_Step2' and spcode='0'