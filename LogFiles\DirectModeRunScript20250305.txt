
08:45:59 - e5689845 - Info     PROPHET_RESULTS_DIR not available
08:46:17 - e5689845 - Step Inforeading results settings
08:46:17 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results
08:46:17 - e5689845 - Info     requestDescription: EMO_RP202501_Step3
08:46:17 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step3.py
08:46:17 - e5689845 - Info     resultsLoadingFlag: yes
08:46:17 - e5689845 - Info     csvLoadingFlag: no
08:46:17 - e5689845 - Info     extractionFlag: yes
08:46:17 - e5689845 - Info     update_t_AggPrjData: no
08:46:17 - e5689845 - Info     update_t_IMPData: no
08:46:17 - e5689845 - Info     update_t_PRJData: yes
08:46:17 - e5689845 - Info     update_t_StoMData: no
08:46:17 - e5689845 - Info     update_t_StoYData: no
08:46:17 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [1],
            "products": ["C_TERM"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1]
            },
            "monthFirst": 1,
            "monthTimes": 12
08:46:17 - e5689845 - Info     variable_filter: 'AND r2_1.value::text in (''ACCUM_TYPE'', ''ANNUITY_FAC'', ''ANNUITY_IF'', ''ANUITY_OUTGO'', ''PREMS_PAID_M'', ''NET_INVT_RET'', ''L_VAL'', ''PREM_FRAC_PP'', ''GROSS_INVRET'', ''CALENDAR_MTH'', ''CALENDAR_YR'', ''L_VAL_M'', ''IMM_EXP_REL'', ''START_PVFP_A'', ''START_PVFP_B'', ''START_PVFP_C'')'
08:46:17 - e5689845 - Step InfoCreating new load request
08:46:17 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
08:46:17 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "EMO_RP202501_Step3",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-03-05T08:46:17",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [1],
            "products": ["C_TERM"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1]
            },
            "monthFirst": 1,
            "monthTimes": 12
        }
    }
}
08:46:20 - e5689845 - Info     <Response [201]>
08:46:20 - e5689845 - Info     Request was successful.
08:46:20 - e5689845 - Step Infoconnecting to postgres control database
08:46:20 - e5689845 - Info     connected to controldb_idr
08:46:25 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
08:46:25 - e5689845 - Info     requestid: 124
08:46:25 - e5689845 - Info     requestdescription: EMO_RP202501_Step3
08:46:25 - e5689845 - Info     request (124) in progress (status 4)
08:46:25 - e5689845 - Info     wait 10s for next status check
08:46:35 - e5689845 - Info     request (124) in progress (status 4)
08:46:35 - e5689845 - Info     wait 10s for next status check
08:46:46 - e5689845 - Info     request (124) in progress (status 4)
08:46:46 - e5689845 - Info     wait 10s for next status check
08:46:56 - e5689845 - Info     request (124) in progress (status 4)
08:46:56 - e5689845 - Info     wait 10s for next status check
08:47:06 - e5689845 - Info     request (124) in progress (status 4)
08:47:06 - e5689845 - Info     wait 10s for next status check
08:47:16 - e5689845 - Info     request (124) in progress (status 4)
08:47:16 - e5689845 - Info     wait 10s for next status check
08:47:26 - e5689845 - Info     request (124) in progress (status 4)
08:47:26 - e5689845 - Info     wait 10s for next status check
08:47:36 - e5689845 - Info     request (124) in progress (status 4)
08:47:36 - e5689845 - Info     wait 10s for next status check
08:47:46 - e5689845 - Info     request (124) in progress (status 4)
08:47:46 - e5689845 - Info     wait 10s for next status check
08:47:56 - e5689845 - Info     request (124) in progress (status 4)
08:47:56 - e5689845 - Info     wait 10s for next status check
08:48:06 - e5689845 - Info     request (124) in progress (status 4)
08:48:06 - e5689845 - Info     wait 10s for next status check
08:48:16 - e5689845 - Info     request (124) in progress (status 4)
08:48:16 - e5689845 - Info     wait 10s for next status check
08:48:26 - e5689845 - Info     request (124) in progress (status 4)
08:48:26 - e5689845 - Info     wait 10s for next status check
08:48:36 - e5689845 - Info     request (124) in progress (status 4)
08:48:36 - e5689845 - Info     wait 10s for next status check
08:48:46 - e5689845 - Info     request (124) in progress (status 4)
08:48:46 - e5689845 - Info     wait 10s for next status check
08:48:56 - e5689845 - Info     request (124) in progress (status 4)
08:48:56 - e5689845 - Info     wait 10s for next status check
08:49:06 - e5689845 - Info     request (124) in progress (status 4)
08:49:06 - e5689845 - Info     wait 10s for next status check
08:49:16 - e5689845 - Info     request (124) in progress (status 4)
08:49:16 - e5689845 - Info     wait 10s for next status check
08:49:26 - e5689845 - Info     request (124) in progress (status 4)
08:49:26 - e5689845 - Info     wait 10s for next status check
08:49:36 - e5689845 - Info     request (124) in progress (status 4)
08:49:36 - e5689845 - Info     wait 10s for next status check
08:49:46 - e5689845 - Info     request (124) in progress (status 4)
08:49:46 - e5689845 - Info     wait 10s for next status check
08:49:56 - e5689845 - Info     request (124) in progress (status 4)
08:49:56 - e5689845 - Info     wait 10s for next status check
08:50:06 - e5689845 - Info     request (124) in progress (status 4)
08:50:06 - e5689845 - Info     wait 10s for next status check
08:50:16 - e5689845 - Info     request (124) in progress (status 4)
08:50:16 - e5689845 - Info     wait 10s for next status check
08:50:26 - e5689845 - Info     request (124) in progress (status 4)
08:50:26 - e5689845 - Info     wait 10s for next status check
08:50:36 - e5689845 - Info     request (124) in progress (status 4)
08:50:36 - e5689845 - Info     wait 10s for next status check
08:50:46 - e5689845 - Info     request (124) in progress (status 4)
08:50:46 - e5689845 - Info     wait 10s for next status check
08:50:56 - e5689845 - Info     request (124) in progress (status 4)
08:50:56 - e5689845 - Info     wait 10s for next status check
08:51:06 - e5689845 - Info     request (124) in progress (status 4)
08:51:06 - e5689845 - Info     wait 10s for next status check
08:51:16 - e5689845 - Info     request (124) in progress (status 4)
08:51:16 - e5689845 - Info     wait 10s for next status check
08:51:26 - e5689845 - Info     request (124) in progress (status 4)
08:51:26 - e5689845 - Info     wait 10s for next status check
08:51:36 - e5689845 - Info     request (124) in progress (status 4)
08:51:36 - e5689845 - Info     wait 10s for next status check
08:51:46 - e5689845 - Info     request (124) in progress (status 4)
08:51:46 - e5689845 - Info     wait 10s for next status check
08:51:56 - e5689845 - Info     request (124) in progress (status 4)
08:51:56 - e5689845 - Info     wait 10s for next status check
08:52:06 - e5689845 - Info     request (124) in progress (status 4)
08:52:06 - e5689845 - Info     wait 10s for next status check
08:52:16 - e5689845 - Info     request (124) in progress (status 4)
08:52:16 - e5689845 - Info     wait 10s for next status check
08:52:26 - e5689845 - Info     request (124) in progress (status 4)
08:52:26 - e5689845 - Info     wait 10s for next status check
08:52:36 - e5689845 - Info     request (124) in progress (status 4)
08:52:36 - e5689845 - Info     wait 10s for next status check
08:52:46 - e5689845 - Info     request (124) in progress (status 4)
08:52:46 - e5689845 - Info     wait 10s for next status check
08:52:56 - e5689845 - Info     request (124) in progress (status 4)
08:52:56 - e5689845 - Info     wait 10s for next status check
08:53:06 - e5689845 - Info     request (124) in progress (status 4)
08:53:06 - e5689845 - Info     wait 10s for next status check
08:53:16 - e5689845 - Info     request (124) in progress (status 4)
08:53:16 - e5689845 - Info     wait 10s for next status check
08:53:26 - e5689845 - Info     request (124) in progress (status 4)
08:53:26 - e5689845 - Info     wait 10s for next status check
08:53:36 - e5689845 - Info     request (124) in progress (status 4)
08:53:36 - e5689845 - Info     wait 10s for next status check
08:53:46 - e5689845 - Info     request (124) in progress (status 4)
08:53:46 - e5689845 - Info     wait 10s for next status check
08:53:56 - e5689845 - Info     request (124) in progress (status 4)
08:53:56 - e5689845 - Info     wait 10s for next status check
08:54:06 - e5689845 - Info     request (124) in progress (status 4)
08:54:06 - e5689845 - Info     wait 10s for next status check
08:54:16 - e5689845 - Info     request (124) in progress (status 4)
08:54:16 - e5689845 - Info     wait 10s for next status check
08:54:26 - e5689845 - Info     request (124) in progress (status 4)
08:54:26 - e5689845 - Info     wait 10s for next status check
08:54:36 - e5689845 - Info     request (124) in progress (status 4)
08:54:36 - e5689845 - Info     wait 10s for next status check
08:54:47 - e5689845 - Info     request (124) in progress (status 4)
08:54:47 - e5689845 - Info     wait 10s for next status check
08:54:57 - e5689845 - Info     request (124) in progress (status 4)
08:54:57 - e5689845 - Info     wait 10s for next status check
08:55:07 - e5689845 - Info     request (124) in progress (status 4)
08:55:07 - e5689845 - Info     wait 10s for next status check
08:55:17 - e5689845 - Info     request (124) in progress (status 4)
08:55:17 - e5689845 - Info     wait 10s for next status check
08:55:27 - e5689845 - Info     request (124) in progress (status 4)
08:55:27 - e5689845 - Info     wait 10s for next status check
08:55:37 - e5689845 - Info     request (124) in progress (status 4)
08:55:37 - e5689845 - Info     wait 10s for next status check
08:55:47 - e5689845 - Info     request (124) in progress (status 4)
08:55:47 - e5689845 - Info     wait 10s for next status check
08:55:57 - e5689845 - Info     request (124) in progress (status 4)
08:55:57 - e5689845 - Info     wait 10s for next status check
08:56:07 - e5689845 - Info     request (124) in progress (status 4)
08:56:07 - e5689845 - Info     wait 10s for next status check
08:56:17 - e5689845 - Info     request (124) in progress (status 4)
08:56:17 - e5689845 - Info     wait 10s for next status check
08:56:27 - e5689845 - Info     request (124) in progress (status 4)
08:56:27 - e5689845 - Info     wait 10s for next status check
08:56:37 - e5689845 - Info     request (124) in progress (status 6)
08:56:37 - e5689845 - Error    request failed, please check db log for more information
08:58:02 - e5689845 - Info     PROPHET_RESULTS_DIR not available
08:58:17 - e5689845 - Step Inforeading results settings
08:58:17 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results
08:58:17 - e5689845 - Info     requestDescription: EMO_RP202501_Step3
08:58:17 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step3.py
08:58:17 - e5689845 - Info     resultsLoadingFlag: yes
08:58:17 - e5689845 - Info     csvLoadingFlag: no
08:58:17 - e5689845 - Info     extractionFlag: yes
08:58:17 - e5689845 - Info     update_t_AggPrjData: no
08:58:17 - e5689845 - Info     update_t_IMPData: no
08:58:17 - e5689845 - Info     update_t_PRJData: yes
08:58:17 - e5689845 - Info     update_t_StoMData: no
08:58:17 - e5689845 - Info     update_t_StoYData: no
08:58:17 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [1],
            "products": ["C_TERM"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1]
            },
            "monthFirst": 1,
            "monthTimes": 12
08:58:17 - e5689845 - Info     variable_filter: 'AND r2_1.value::text in (''ACCUM_TYPE'', ''ANNUITY_FAC'', ''ANNUITY_IF'', ''ANUITY_OUTGO'', ''PREMS_PAID_M'', ''NET_INVT_RET'', ''L_VAL'', ''PREM_FRAC_PP'', ''GROSS_INVRET'', ''CALENDAR_MTH'', ''CALENDAR_YR'', ''L_VAL_M'', ''IMM_EXP_REL'', ''START_PVFP_A'', ''START_PVFP_B'', ''START_PVFP_C'')'
08:58:17 - e5689845 - Step InfoCreating new load request
08:58:17 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
08:58:17 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "EMO_RP202501_Step3",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-03-05T08:58:17",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [1],
            "products": ["C_TERM"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1]
            },
            "monthFirst": 1,
            "monthTimes": 12
        }
    }
}
08:58:17 - e5689845 - Info     <Response [201]>
08:58:18 - e5689845 - Info     Request was successful.
08:58:18 - e5689845 - Step Infoconnecting to postgres control database
08:58:18 - e5689845 - Info     connected to controldb_idr
08:58:23 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
08:58:23 - e5689845 - Info     requestid: 125
08:58:23 - e5689845 - Info     requestdescription: EMO_RP202501_Step3
08:58:23 - e5689845 - Info     request (125) in progress (status 3)
08:58:23 - e5689845 - Info     wait 10s for next status check
08:58:33 - e5689845 - Info     request (125) in progress (status 3)
08:58:33 - e5689845 - Info     wait 10s for next status check
08:58:43 - e5689845 - Info     request (125) in progress (status 3)
08:58:43 - e5689845 - Info     wait 10s for next status check
08:58:53 - e5689845 - Info     request (125) in progress (status 4)
08:58:53 - e5689845 - Info     wait 10s for next status check
08:59:03 - e5689845 - Info     request (125) in progress (status 4)
08:59:03 - e5689845 - Info     wait 10s for next status check
08:59:13 - e5689845 - Info     request (125) in progress (status 4)
08:59:13 - e5689845 - Info     wait 10s for next status check
08:59:23 - e5689845 - Info     request (125) in progress (status 4)
08:59:23 - e5689845 - Info     wait 10s for next status check
08:59:33 - e5689845 - Info     request (125) in progress (status 4)
08:59:33 - e5689845 - Info     wait 10s for next status check
08:59:43 - e5689845 - Info     request (125) in progress (status 4)
08:59:43 - e5689845 - Info     wait 10s for next status check
08:59:53 - e5689845 - Info     request (125) in progress (status 4)
08:59:53 - e5689845 - Info     wait 10s for next status check
09:00:03 - e5689845 - Info     request (125) in progress (status 4)
09:00:03 - e5689845 - Info     wait 10s for next status check
09:00:13 - e5689845 - Info     request (125) in progress (status 4)
09:00:13 - e5689845 - Info     wait 10s for next status check
09:00:23 - e5689845 - Info     request (125) in progress (status 4)
09:00:23 - e5689845 - Info     wait 10s for next status check
09:00:33 - e5689845 - Info     request (125) in progress (status 4)
09:00:33 - e5689845 - Info     wait 10s for next status check
09:00:43 - e5689845 - Info     request (125) in progress (status 4)
09:00:43 - e5689845 - Info     wait 10s for next status check
09:00:53 - e5689845 - Info     request (125) in progress (status 4)
09:00:53 - e5689845 - Info     wait 10s for next status check
09:01:03 - e5689845 - Info     request (125) in progress (status 4)
09:01:03 - e5689845 - Info     wait 10s for next status check
09:01:13 - e5689845 - Info     request (125) in progress (status 4)
09:01:13 - e5689845 - Info     wait 10s for next status check
09:01:23 - e5689845 - Info     request (125) in progress (status 4)
09:01:23 - e5689845 - Info     wait 10s for next status check
09:01:33 - e5689845 - Info     request (125) in progress (status 4)
09:01:33 - e5689845 - Info     wait 10s for next status check
09:01:43 - e5689845 - Info     request (125) in progress (status 4)
09:01:43 - e5689845 - Info     wait 10s for next status check
09:01:53 - e5689845 - Info     request (125) in progress (status 4)
09:01:53 - e5689845 - Info     wait 10s for next status check
09:02:03 - e5689845 - Info     request (125) in progress (status 4)
09:02:03 - e5689845 - Info     wait 10s for next status check
09:02:13 - e5689845 - Info     request (125) in progress (status 4)
09:02:13 - e5689845 - Info     wait 10s for next status check
09:02:23 - e5689845 - Info     request (125) in progress (status 4)
09:02:23 - e5689845 - Info     wait 10s for next status check
09:02:33 - e5689845 - Info     request (125) in progress (status 4)
09:02:33 - e5689845 - Info     wait 10s for next status check
09:02:43 - e5689845 - Info     request (125) in progress (status 4)
09:02:43 - e5689845 - Info     wait 10s for next status check
09:02:53 - e5689845 - Info     request (125) in progress (status 4)
09:02:53 - e5689845 - Info     wait 10s for next status check
09:03:03 - e5689845 - Info     request (125) in progress (status 4)
09:03:03 - e5689845 - Info     wait 10s for next status check
09:03:13 - e5689845 - Info     request (125) in progress (status 4)
09:03:13 - e5689845 - Info     wait 10s for next status check
09:03:23 - e5689845 - Info     request (125) in progress (status 4)
09:03:23 - e5689845 - Info     wait 10s for next status check
09:03:33 - e5689845 - Info     request (125) in progress (status 4)
09:03:33 - e5689845 - Info     wait 10s for next status check
09:03:43 - e5689845 - Info     request (125) in progress (status 4)
09:03:43 - e5689845 - Info     wait 10s for next status check
09:03:53 - e5689845 - Info     request (125) in progress (status 4)
09:03:53 - e5689845 - Info     wait 10s for next status check
09:04:03 - e5689845 - Info     request (125) in progress (status 4)
09:04:03 - e5689845 - Info     wait 10s for next status check
09:04:13 - e5689845 - Info     request (125) in progress (status 5)
09:04:13 - e5689845 - Success  request completed
09:04:13 - e5689845 - Step Infoconnecting to postgres results database
09:04:13 - e5689845 - Info     connected to resultsdb_idr
09:04:13 - e5689845 - Info     updating t_PRJData
09:04:13 - e5689845 - Info     CALL "Analytics".spLoad_t_PRJData(125,'AND r2_1.value::text in (''ACCUM_TYPE'', ''ANNUITY_FAC'', ''ANNUITY_IF'', ''ANUITY_OUTGO'', ''PREMS_PAID_M'', ''NET_INVT_RET'', ''L_VAL'', ''PREM_FRAC_PP'', ''GROSS_INVRET'', ''CALENDAR_MTH'', ''CALENDAR_YR'', ''L_VAL_M'', ''IMM_EXP_REL'', ''START_PVFP_A'', ''START_PVFP_B'', ''START_PVFP_C'')'::text)
09:04:20 - e5689845 - Info     NOTICE:  Deleted 0 rows from t_PRJData for resultslocation=\\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results and requestdescription=EMO_RP202501_Step3

09:04:20 - e5689845 - Info     NOTICE:  Loaded 132 rows into t_PRJData for requestid=125 

09:04:20 - e5689845 - Step InfoGenerating extraction
09:04:20 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\EMO_202501.csv
09:04:20 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where requestdescription = 'EMO_RP202501_Step1' and spcode='0'
09:04:27 - e5689845 - Success  extraction completed