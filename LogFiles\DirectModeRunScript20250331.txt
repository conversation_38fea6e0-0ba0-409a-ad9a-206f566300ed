
06:58:18 - e5689845 - Info     PROPHET_RESULTS_DIR not available
06:58:40 - e5689845 - Step Inforeading results settings
06:58:40 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results
06:58:40 - e5689845 - Info     requestDescription: EMO_RP202501_Step3
06:58:40 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step3.py
06:58:40 - e5689845 - Info     resultsLoadingFlag: yes
06:58:40 - e5689845 - Info     csvLoadingFlag: no
06:58:40 - e5689845 - Info     extractionFlag: yes
06:58:40 - e5689845 - Info     update_t_AggPrjData: no
06:58:40 - e5689845 - Info     update_t_IMPData: no
06:58:40 - e5689845 - Info     update_t_PRJData: yes
06:58:40 - e5689845 - Info     update_t_StoMData: no
06:58:40 - e5689845 - Info     update_t_StoYData: no
06:58:40 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [1],
            "products": ["C_TERM"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1]
            },
            "monthFirst": 1,
            "monthTimes": 12
06:58:40 - e5689845 - Info     variable_filter: 'AND r2_1.value::text in (''ACCUM_TYPE'', ''ANNUITY_FAC'', ''ANNUITY_IF'', ''ANUITY_OUTGO'', ''PREMS_PAID_M'', ''NET_INVT_RET'', ''L_VAL'', ''PREM_FRAC_PP'', ''GROSS_INVRET'', ''CALENDAR_MTH'', ''CALENDAR_YR'', ''L_VAL_M'', ''IMM_EXP_REL'', ''START_PVFP_A'', ''START_PVFP_B'', ''START_PVFP_C'')'
06:58:40 - e5689845 - Step InfoCreating new load request
06:58:40 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
06:58:40 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "EMO_RP202501_Step3",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-03-31T06:58:40",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [1],
            "products": ["C_TERM"],
            "resultsTypes": ["prj"],
            "spCodes": [0, 1]
            },
            "monthFirst": 1,
            "monthTimes": 12
        }
    }
}
06:58:44 - e5689845 - Info     <Response [201]>
06:58:44 - e5689845 - Info     Request was successful.
06:58:44 - e5689845 - Step Infoconnecting to postgres control database
06:58:44 - e5689845 - Info     connected to controldb_idr
06:58:49 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
06:58:49 - e5689845 - Info     requestid: 131
06:58:49 - e5689845 - Info     requestdescription: EMO_RP202501_Step3
06:58:49 - e5689845 - Info     request (131) in progress (status 3)
06:58:49 - e5689845 - Info     wait 10s for next status check
06:58:59 - e5689845 - Info     request (131) in progress (status 4)
06:58:59 - e5689845 - Info     wait 10s for next status check
06:59:09 - e5689845 - Info     request (131) in progress (status 4)
06:59:09 - e5689845 - Info     wait 10s for next status check
06:59:19 - e5689845 - Info     request (131) in progress (status 4)
06:59:19 - e5689845 - Info     wait 10s for next status check
06:59:29 - e5689845 - Info     request (131) in progress (status 4)
06:59:29 - e5689845 - Info     wait 10s for next status check
06:59:39 - e5689845 - Info     request (131) in progress (status 4)
06:59:39 - e5689845 - Info     wait 10s for next status check
06:59:49 - e5689845 - Info     request (131) in progress (status 4)
06:59:49 - e5689845 - Info     wait 10s for next status check
06:59:59 - e5689845 - Info     request (131) in progress (status 4)
06:59:59 - e5689845 - Info     wait 10s for next status check
07:00:09 - e5689845 - Info     request (131) in progress (status 4)
07:00:09 - e5689845 - Info     wait 10s for next status check
07:00:19 - e5689845 - Info     request (131) in progress (status 4)
07:00:19 - e5689845 - Info     wait 10s for next status check
07:00:29 - e5689845 - Info     request (131) in progress (status 4)
07:00:29 - e5689845 - Info     wait 10s for next status check
07:00:39 - e5689845 - Info     request (131) in progress (status 4)
07:00:39 - e5689845 - Info     wait 10s for next status check
07:00:49 - e5689845 - Info     request (131) in progress (status 4)
07:00:49 - e5689845 - Info     wait 10s for next status check
07:00:59 - e5689845 - Info     request (131) in progress (status 4)
07:00:59 - e5689845 - Info     wait 10s for next status check
07:01:09 - e5689845 - Info     request (131) in progress (status 4)
07:01:09 - e5689845 - Info     wait 10s for next status check
07:01:19 - e5689845 - Info     request (131) in progress (status 4)
07:01:19 - e5689845 - Info     wait 10s for next status check
07:01:29 - e5689845 - Info     request (131) in progress (status 4)
07:01:29 - e5689845 - Info     wait 10s for next status check
07:01:39 - e5689845 - Info     request (131) in progress (status 4)
07:01:39 - e5689845 - Info     wait 10s for next status check
07:01:49 - e5689845 - Info     request (131) in progress (status 4)
07:01:49 - e5689845 - Info     wait 10s for next status check
07:01:59 - e5689845 - Info     request (131) in progress (status 4)
07:01:59 - e5689845 - Info     wait 10s for next status check
07:02:09 - e5689845 - Info     request (131) in progress (status 4)
07:02:09 - e5689845 - Info     wait 10s for next status check
07:02:19 - e5689845 - Info     request (131) in progress (status 4)
07:02:19 - e5689845 - Info     wait 10s for next status check
07:02:29 - e5689845 - Info     request (131) in progress (status 4)
07:02:29 - e5689845 - Info     wait 10s for next status check
07:02:40 - e5689845 - Info     request (131) in progress (status 4)
07:02:40 - e5689845 - Info     wait 10s for next status check
07:02:50 - e5689845 - Info     request (131) in progress (status 4)
07:02:50 - e5689845 - Info     wait 10s for next status check
07:03:00 - e5689845 - Info     request (131) in progress (status 4)
07:03:00 - e5689845 - Info     wait 10s for next status check
07:03:10 - e5689845 - Info     request (131) in progress (status 4)
07:03:10 - e5689845 - Info     wait 10s for next status check
07:03:20 - e5689845 - Info     request (131) in progress (status 4)
07:03:20 - e5689845 - Info     wait 10s for next status check
07:03:30 - e5689845 - Info     request (131) in progress (status 4)
07:03:30 - e5689845 - Info     wait 10s for next status check
07:03:40 - e5689845 - Info     request (131) in progress (status 4)
07:03:40 - e5689845 - Info     wait 10s for next status check
07:03:50 - e5689845 - Info     request (131) in progress (status 4)
07:03:50 - e5689845 - Info     wait 10s for next status check
07:04:00 - e5689845 - Info     request (131) in progress (status 4)
07:04:00 - e5689845 - Info     wait 10s for next status check
07:04:10 - e5689845 - Info     request (131) in progress (status 5)
07:04:10 - e5689845 - Success  request completed
07:04:10 - e5689845 - Step Infoconnecting to postgres results database
07:04:10 - e5689845 - Info     connected to resultsdb_idr
07:04:10 - e5689845 - Info     updating t_PRJData
07:04:10 - e5689845 - Info     CALL "Analytics".spLoad_t_PRJData(131,'AND r2_1.value::text in (''ACCUM_TYPE'', ''ANNUITY_FAC'', ''ANNUITY_IF'', ''ANUITY_OUTGO'', ''PREMS_PAID_M'', ''NET_INVT_RET'', ''L_VAL'', ''PREM_FRAC_PP'', ''GROSS_INVRET'', ''CALENDAR_MTH'', ''CALENDAR_YR'', ''L_VAL_M'', ''IMM_EXP_REL'', ''START_PVFP_A'', ''START_PVFP_B'', ''START_PVFP_C'')'::text)
07:04:17 - e5689845 - Info     NOTICE:  Deleted 132 rows from t_PRJData for resultslocation=\\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results and requestdescription=EMO_RP202501_Step3

07:04:17 - e5689845 - Info     NOTICE:  Loaded 132 rows into t_PRJData for requestid=131 

07:04:17 - e5689845 - Step InfoGenerating extraction
07:04:17 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\EMO_202501.csv
07:04:17 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where requestdescription = 'EMO_RP202501_Step3' and spcode='0'
07:04:24 - e5689845 - Success  extraction completed
08:20:36 - e5689845 - Info     PROPHET_RESULTS_DIR not available
08:22:06 - e5689845 - Info     PROPHET_RESULTS_DIR not available
08:22:24 - e5689845 - Step Inforeading results settings
08:22:24 - e5689845 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results
08:22:24 - e5689845 - Info     requestDescription: EMO_RP202501_Step3
08:22:24 - e5689845 - Info     Reading Step Config file: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\StepConfig\Step3.py
08:22:24 - e5689845 - Info     resultsLoadingFlag: yes
08:22:24 - e5689845 - Info     csvLoadingFlag: no
08:22:24 - e5689845 - Info     extractionFlag: yes
08:22:24 - e5689845 - Info     update_t_AggPrjData: no
08:22:24 - e5689845 - Info     update_t_IMPData: yes
08:22:24 - e5689845 - Info     update_t_PRJData: yes
08:22:24 - e5689845 - Info     update_t_StoMData: no
08:22:24 - e5689845 - Info     update_t_StoYData: no
08:22:24 - e5689845 - Info     requestFilters: "requestFilters": {
    
            "runNumbers": [1],
            "products": ["C_TERM"],
            "resultsTypes": ["prj", "imp"],
            "spCodes": [0, 1]
            },
            "monthFirst": 1,
            "monthTimes": 12
08:22:24 - e5689845 - Info     variable_filter: 'AND r2_1.value::text in (''ACCUM_TYPE'', ''ANNUITY_FAC'', ''ANNUITY_IF'', ''ANUITY_OUTGO'', ''PREMS_PAID_M'', ''NET_INVT_RET'', ''L_VAL'', ''PREM_FRAC_PP'', ''GROSS_INVRET'', ''CALENDAR_MTH'', ''CALENDAR_YR'', ''L_VAL_M'', ''IMM_EXP_REL'', ''START_PVFP_A'', ''START_PVFP_B'', ''START_PVFP_C'')'
08:22:24 - e5689845 - Step InfoCreating new load request
08:22:24 - e5689845 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
08:22:24 - e5689845 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "EMO_RP202501_Step3",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-03-31T08:22:24",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            "runNumbers": [1],
            "products": ["C_TERM"],
            "resultsTypes": ["prj", "imp"],
            "spCodes": [0, 1]
            },
            "monthFirst": 1,
            "monthTimes": 12
        }
    }
}
08:22:27 - e5689845 - Info     <Response [201]>
08:22:27 - e5689845 - Info     Request was successful.
08:22:27 - e5689845 - Step Infoconnecting to postgres control database
08:22:27 - e5689845 - Info     connected to controldb_idr
08:22:32 - e5689845 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
08:22:32 - e5689845 - Info     requestid: 132
08:22:32 - e5689845 - Info     requestdescription: EMO_RP202501_Step3
08:22:32 - e5689845 - Info     request (132) in progress (status 3)
08:22:32 - e5689845 - Info     wait 10s for next status check
08:22:42 - e5689845 - Info     request (132) in progress (status 4)
08:22:42 - e5689845 - Info     wait 10s for next status check
08:22:52 - e5689845 - Info     request (132) in progress (status 4)
08:22:52 - e5689845 - Info     wait 10s for next status check
08:23:02 - e5689845 - Info     request (132) in progress (status 4)
08:23:02 - e5689845 - Info     wait 10s for next status check
08:23:12 - e5689845 - Info     request (132) in progress (status 4)
08:23:12 - e5689845 - Info     wait 10s for next status check
08:23:22 - e5689845 - Info     request (132) in progress (status 4)
08:23:22 - e5689845 - Info     wait 10s for next status check
08:23:32 - e5689845 - Info     request (132) in progress (status 4)
08:23:32 - e5689845 - Info     wait 10s for next status check
08:24:25 - e5689845 - Info     request (132) in progress (status 4)
08:24:25 - e5689845 - Info     wait 10s for next status check
08:24:36 - e5689845 - Info     request (132) in progress (status 4)
08:24:36 - e5689845 - Info     wait 10s for next status check
08:24:46 - e5689845 - Info     request (132) in progress (status 4)
08:24:46 - e5689845 - Info     wait 10s for next status check
08:24:56 - e5689845 - Info     request (132) in progress (status 4)
08:24:56 - e5689845 - Info     wait 10s for next status check
08:25:06 - e5689845 - Info     request (132) in progress (status 4)
08:25:06 - e5689845 - Info     wait 10s for next status check
08:25:16 - e5689845 - Info     request (132) in progress (status 4)
08:25:16 - e5689845 - Info     wait 10s for next status check
08:25:26 - e5689845 - Info     request (132) in progress (status 4)
08:25:26 - e5689845 - Info     wait 10s for next status check
08:25:36 - e5689845 - Info     request (132) in progress (status 4)
08:25:36 - e5689845 - Info     wait 10s for next status check
08:25:46 - e5689845 - Info     request (132) in progress (status 4)
08:25:46 - e5689845 - Info     wait 10s for next status check
08:25:56 - e5689845 - Info     request (132) in progress (status 4)
08:25:56 - e5689845 - Info     wait 10s for next status check
08:26:06 - e5689845 - Info     request (132) in progress (status 4)
08:26:06 - e5689845 - Info     wait 10s for next status check
08:26:16 - e5689845 - Info     request (132) in progress (status 4)
08:26:16 - e5689845 - Info     wait 10s for next status check
08:26:26 - e5689845 - Info     request (132) in progress (status 4)
08:26:26 - e5689845 - Info     wait 10s for next status check
08:26:36 - e5689845 - Info     request (132) in progress (status 4)
08:26:36 - e5689845 - Info     wait 10s for next status check
08:26:46 - e5689845 - Info     request (132) in progress (status 4)
08:26:46 - e5689845 - Info     wait 10s for next status check
08:26:56 - e5689845 - Info     request (132) in progress (status 4)
08:26:56 - e5689845 - Info     wait 10s for next status check
08:27:06 - e5689845 - Info     request (132) in progress (status 4)
08:27:06 - e5689845 - Info     wait 10s for next status check
08:27:16 - e5689845 - Info     request (132) in progress (status 4)
08:27:16 - e5689845 - Info     wait 10s for next status check
08:27:26 - e5689845 - Info     request (132) in progress (status 4)
08:27:26 - e5689845 - Info     wait 10s for next status check
08:27:36 - e5689845 - Info     request (132) in progress (status 4)
08:27:36 - e5689845 - Info     wait 10s for next status check
08:27:46 - e5689845 - Info     request (132) in progress (status 4)
08:27:46 - e5689845 - Info     wait 10s for next status check
08:27:56 - e5689845 - Info     request (132) in progress (status 4)
08:27:56 - e5689845 - Info     wait 10s for next status check
08:28:06 - e5689845 - Info     request (132) in progress (status 5)
08:28:06 - e5689845 - Success  request completed
08:28:06 - e5689845 - Step Infoconnecting to postgres results database
08:28:06 - e5689845 - Info     connected to resultsdb_idr
08:28:06 - e5689845 - Info     updating t_IMPData
08:28:06 - e5689845 - Info     CALL "Analytics".spLoad_t_IMPData(132,'AND r2_1.value::text in (''ACCUM_TYPE'', ''ANNUITY_FAC'', ''ANNUITY_IF'', ''ANUITY_OUTGO'', ''PREMS_PAID_M'', ''NET_INVT_RET'', ''L_VAL'', ''PREM_FRAC_PP'', ''GROSS_INVRET'', ''CALENDAR_MTH'', ''CALENDAR_YR'', ''L_VAL_M'', ''IMM_EXP_REL'', ''START_PVFP_A'', ''START_PVFP_B'', ''START_PVFP_C'')'::text)
08:28:06 - e5689845 - Info     updating t_PRJData
08:28:06 - e5689845 - Info     CALL "Analytics".spLoad_t_PRJData(132,'AND r2_1.value::text in (''ACCUM_TYPE'', ''ANNUITY_FAC'', ''ANNUITY_IF'', ''ANUITY_OUTGO'', ''PREMS_PAID_M'', ''NET_INVT_RET'', ''L_VAL'', ''PREM_FRAC_PP'', ''GROSS_INVRET'', ''CALENDAR_MTH'', ''CALENDAR_YR'', ''L_VAL_M'', ''IMM_EXP_REL'', ''START_PVFP_A'', ''START_PVFP_B'', ''START_PVFP_C'')'::text)
08:28:13 - e5689845 - Info     NOTICE:  Deleted 0 rows from t_IMPData for resultslocation=\\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results and requestdescription=EMO_RP202501_Step3

08:28:13 - e5689845 - Info     NOTICE:  Loaded 5 rows into t_IMPData for requestid=132 

08:28:13 - e5689845 - Info     NOTICE:  Deleted 384 rows from t_PRJData for resultslocation=\\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results and requestdescription=EMO_RP202501_Step3

08:28:13 - e5689845 - Info     NOTICE:  Loaded 384 rows into t_PRJData for requestid=132 

08:28:13 - e5689845 - Step InfoGenerating extraction
08:28:13 - e5689845 - Info     extracting \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\DirectModePackage\Reports\EMO_202501.csv
08:28:13 - e5689845 - Info     extract_query: select * from "Analytics".vBI_PRJData where requestdescription = 'EMO_RP202501_Step3' and spcode='0'
08:28:20 - e5689845 - Success  extraction completed