
13:32:13 - e5604064 - Info     PROPHET_RESULTS_DIR not available
13:33:10 - e5604064 - Step Inforeading results settings
13:33:10 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
13:33:10 - e5604064 - Info     requestDescription: Test_Snowflake_202504301333
13:33:10 - e5604064 - Info     No Step config
13:33:10 - e5604064 - Info     resultsLoadingFlag: yes
13:33:10 - e5604064 - Info     csvLoadingFlag: no
13:33:10 - e5604064 - Info     extractionFlag: no
13:33:10 - e5604064 - Info     update_t_AggPrjData: no
13:33:10 - e5604064 - Info     update_t_IMPData: no
13:33:10 - e5604064 - Info     update_t_PRJData: no
13:33:10 - e5604064 - Info     update_t_StoMData: no
13:33:10 - e5604064 - Info     update_t_StoYData: no
13:33:10 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
13:33:10 - e5604064 - Info     variable_filter: ''
13:33:10 - e5604064 - Step InfoCreating new load request
13:33:10 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
13:33:10 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "Test_Snowflake_202504301333",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T13:33:10",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
13:33:13 - e5604064 - Info     <Response [201]>
13:33:13 - e5604064 - Info     Request was successful.
13:33:13 - e5604064 - Step Infoconnecting to Snowflake control database
13:33:13 - e5604064 - Error    Error: name 'create_engine' is not defined
13:37:28 - e5604064 - Info     PROPHET_RESULTS_DIR not available
13:37:59 - e5604064 - Step Inforeading results settings
13:37:59 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
13:37:59 - e5604064 - Info     requestDescription: test_snowflake
13:37:59 - e5604064 - Info     No Step config
13:37:59 - e5604064 - Info     resultsLoadingFlag: yes
13:37:59 - e5604064 - Info     csvLoadingFlag: no
13:37:59 - e5604064 - Info     extractionFlag: no
13:37:59 - e5604064 - Info     update_t_AggPrjData: no
13:37:59 - e5604064 - Info     update_t_IMPData: no
13:37:59 - e5604064 - Info     update_t_PRJData: no
13:37:59 - e5604064 - Info     update_t_StoMData: no
13:37:59 - e5604064 - Info     update_t_StoYData: no
13:37:59 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
13:37:59 - e5604064 - Info     variable_filter: ''
13:37:59 - e5604064 - Step InfoCreating new load request
13:37:59 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
13:37:59 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "test_snowflake",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T13:37:59",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
13:37:59 - e5604064 - Info     <Response [201]>
13:38:00 - e5604064 - Info     Request was successful.
13:38:00 - e5604064 - Step Infoconnecting to Snowflake control database
13:38:00 - e5604064 - Error    Error: name 'snowflakeControlDbConnect' is not defined
13:47:11 - e5604064 - Info     PROPHET_RESULTS_DIR not available
13:47:30 - e5604064 - Step Inforeading results settings
13:47:30 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
13:47:30 - e5604064 - Info     requestDescription: test_snowflake
13:47:30 - e5604064 - Info     No Step config
13:47:30 - e5604064 - Info     resultsLoadingFlag: yes
13:47:30 - e5604064 - Info     csvLoadingFlag: no
13:47:30 - e5604064 - Info     extractionFlag: no
13:47:30 - e5604064 - Info     update_t_AggPrjData: no
13:47:30 - e5604064 - Info     update_t_IMPData: no
13:47:30 - e5604064 - Info     update_t_PRJData: no
13:47:30 - e5604064 - Info     update_t_StoMData: no
13:47:30 - e5604064 - Info     update_t_StoYData: no
13:47:30 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
13:47:30 - e5604064 - Info     variable_filter: ''
13:47:30 - e5604064 - Step InfoCreating new load request
13:47:30 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
13:47:30 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "test_snowflake",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T13:47:30",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
13:47:31 - e5604064 - Info     <Response [201]>
13:47:31 - e5604064 - Info     Request was successful.
13:47:31 - e5604064 - Step Infoconnecting to Snowflake control database
13:47:31 - e5604064 - Error    Error: (snowflake.connector.errors.ProgrammingError) 251001: Account must be specified
(Background on this error at: https://sqlalche.me/e/20/f405)
13:50:48 - e5604064 - Info     PROPHET_RESULTS_DIR not available
13:51:03 - e5604064 - Step Inforeading results settings
13:51:03 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
13:51:03 - e5604064 - Info     requestDescription: test_snowflake
13:51:03 - e5604064 - Info     No Step config
13:51:03 - e5604064 - Info     resultsLoadingFlag: yes
13:51:03 - e5604064 - Info     csvLoadingFlag: no
13:51:03 - e5604064 - Info     extractionFlag: no
13:51:03 - e5604064 - Info     update_t_AggPrjData: no
13:51:03 - e5604064 - Info     update_t_IMPData: no
13:51:03 - e5604064 - Info     update_t_PRJData: no
13:51:03 - e5604064 - Info     update_t_StoMData: no
13:51:03 - e5604064 - Info     update_t_StoYData: no
13:51:03 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
13:51:03 - e5604064 - Info     variable_filter: ''
13:51:03 - e5604064 - Step InfoCreating new load request
13:51:03 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
13:51:03 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "test_snowflake",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T13:51:03",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
13:51:04 - e5604064 - Info     <Response [201]>
13:51:04 - e5604064 - Info     Request was successful.
13:51:04 - e5604064 - Step Infoconnecting to Snowflake control database
13:51:04 - e5604064 - Error    Error: (snowflake.connector.errors.ProgrammingError) 251001: Account must be specified
(Background on this error at: https://sqlalche.me/e/20/f405)
13:55:57 - e5604064 - Info     PROPHET_RESULTS_DIR not available
13:56:16 - e5604064 - Step Inforeading results settings
13:56:16 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
13:56:16 - e5604064 - Info     requestDescription: test_snowflake
13:56:16 - e5604064 - Info     No Step config
13:56:16 - e5604064 - Info     resultsLoadingFlag: yes
13:56:16 - e5604064 - Info     csvLoadingFlag: no
13:56:16 - e5604064 - Info     extractionFlag: no
13:56:16 - e5604064 - Info     update_t_AggPrjData: no
13:56:16 - e5604064 - Info     update_t_IMPData: no
13:56:16 - e5604064 - Info     update_t_PRJData: no
13:56:16 - e5604064 - Info     update_t_StoMData: no
13:56:16 - e5604064 - Info     update_t_StoYData: no
13:56:16 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
13:56:16 - e5604064 - Info     variable_filter: ''
13:56:16 - e5604064 - Step InfoCreating new load request
13:56:16 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
13:56:16 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "test_snowflake",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T13:56:16",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
13:56:17 - e5604064 - Info     <Response [201]>
13:56:17 - e5604064 - Info     Request was successful.
13:56:17 - e5604064 - Step Infoconnecting to Snowflake control database
13:56:17 - e5604064 - Error    Error: (snowflake.connector.errors.ProgrammingError) 251001: Account must be specified
(Background on this error at: https://sqlalche.me/e/20/f405)
14:08:02 - e5604064 - Info     PROPHET_RESULTS_DIR not available
14:08:22 - e5604064 - Step Inforeading results settings
14:08:22 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
14:08:22 - e5604064 - Info     requestDescription: test
14:08:22 - e5604064 - Info     No Step config
14:08:22 - e5604064 - Info     resultsLoadingFlag: yes
14:08:22 - e5604064 - Info     csvLoadingFlag: no
14:08:22 - e5604064 - Info     extractionFlag: no
14:08:22 - e5604064 - Info     update_t_AggPrjData: no
14:08:22 - e5604064 - Info     update_t_IMPData: no
14:08:22 - e5604064 - Info     update_t_PRJData: no
14:08:22 - e5604064 - Info     update_t_StoMData: no
14:08:22 - e5604064 - Info     update_t_StoYData: no
14:08:22 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
14:08:22 - e5604064 - Info     variable_filter: ''
14:08:22 - e5604064 - Step InfoCreating new load request
14:08:22 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
14:08:22 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "test",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T14:08:22",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
14:08:23 - e5604064 - Info     <Response [201]>
14:08:23 - e5604064 - Info     Request was successful.
14:08:23 - e5604064 - Step Infoconnecting to Snowflake control database
14:08:24 - e5604064 - Error    Error: (snowflake.connector.errors.InterfaceError) 250003 (08001): 404 Not Found: post https://<EMAIL>:443/session/v1/login-request?request_id=331a2fb2-38a5-407d-8e42-ad921de5afd0&databaseName=controldb_idr&schemaName=IDR&warehouse=CAPITAL_MARKETS_IDR__WH&roleName=SERV_RL__SERVICE_IDR_REPORTING&request_guid=4e2bd120-0550-49c2-99e8-b2d7744f01dd
(Background on this error at: https://sqlalche.me/e/20/rvf5)
14:13:45 - e5604064 - Info     PROPHET_RESULTS_DIR not available
14:13:57 - e5604064 - Step Inforeading results settings
14:13:57 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
14:13:57 - e5604064 - Info     requestDescription: 
14:13:57 - e5604064 - Info     No Step config
14:13:57 - e5604064 - Info     resultsLoadingFlag: yes
14:13:57 - e5604064 - Info     csvLoadingFlag: no
14:13:57 - e5604064 - Info     extractionFlag: no
14:13:57 - e5604064 - Info     update_t_AggPrjData: no
14:13:57 - e5604064 - Info     update_t_IMPData: no
14:13:57 - e5604064 - Info     update_t_PRJData: no
14:13:57 - e5604064 - Info     update_t_StoMData: no
14:13:57 - e5604064 - Info     update_t_StoYData: no
14:13:57 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
14:13:57 - e5604064 - Info     variable_filter: ''
14:13:57 - e5604064 - Step InfoCreating new load request
14:13:57 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
14:13:57 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T14:13:57",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
14:13:58 - e5604064 - Info     <Response [201]>
14:13:58 - e5604064 - Info     Request was successful.
14:13:58 - e5604064 - Step Infoconnecting to Snowflake control database
14:13:59 - e5604064 - Error    Error: module 'DirectModeConfig' has no attribute 'snowflakeControlDatabase'
14:19:58 - e5604064 - Info     Connected successfully to Snowflake - Warehouse: CAPITAL_MARKETS_IDR__WH, Database: CONTROLDB_IDR, Schema: IDR
14:27:48 - e5604064 - Info     PROPHET_RESULTS_DIR not available
14:28:00 - e5604064 - Step Inforeading results settings
14:28:00 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
14:28:00 - e5604064 - Info     requestDescription: 
14:28:00 - e5604064 - Info     No Step config
14:28:00 - e5604064 - Info     resultsLoadingFlag: yes
14:28:00 - e5604064 - Info     csvLoadingFlag: no
14:28:00 - e5604064 - Info     extractionFlag: no
14:28:00 - e5604064 - Info     update_t_AggPrjData: no
14:28:00 - e5604064 - Info     update_t_IMPData: no
14:28:00 - e5604064 - Info     update_t_PRJData: no
14:28:00 - e5604064 - Info     update_t_StoMData: no
14:28:00 - e5604064 - Info     update_t_StoYData: no
14:28:00 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
14:28:00 - e5604064 - Info     variable_filter: ''
14:28:00 - e5604064 - Step InfoCreating new load request
14:28:00 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
14:28:00 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T14:28:00",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
14:28:04 - e5604064 - Info     <Response [201]>
14:28:04 - e5604064 - Info     Request was successful.
14:28:04 - e5604064 - Step Infoconnecting to Snowflake control database
14:28:05 - e5604064 - Error    Error: module 'DirectModeConfig' has no attribute 'snowflakeControlDatabase'
14:29:53 - e5604064 - Info     PROPHET_RESULTS_DIR not available
14:30:04 - e5604064 - Step Inforeading results settings
14:30:04 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
14:30:04 - e5604064 - Info     requestDescription: 
14:30:04 - e5604064 - Info     No Step config
14:30:04 - e5604064 - Info     resultsLoadingFlag: yes
14:30:04 - e5604064 - Info     csvLoadingFlag: no
14:30:04 - e5604064 - Info     extractionFlag: no
14:30:04 - e5604064 - Info     update_t_AggPrjData: no
14:30:04 - e5604064 - Info     update_t_IMPData: no
14:30:04 - e5604064 - Info     update_t_PRJData: no
14:30:04 - e5604064 - Info     update_t_StoMData: no
14:30:04 - e5604064 - Info     update_t_StoYData: no
14:30:04 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
14:30:04 - e5604064 - Info     variable_filter: ''
14:30:04 - e5604064 - Step InfoCreating new load request
14:30:04 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
14:30:04 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T14:30:04",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
14:30:04 - e5604064 - Info     <Response [201]>
14:30:04 - e5604064 - Info     Request was successful.
14:30:04 - e5604064 - Step Infoconnecting to Snowflake control database
14:30:05 - e5604064 - Info     connected to controldb_idr
14:30:10 - e5604064 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
14:30:11 - e5604064 - Error    Error: cannot unpack non-iterable NoneType object
17:25:58 - e5604064 - Info     PROPHET_RESULTS_DIR not available
17:27:18 - e5604064 - Step Inforeading results settings
17:27:18 - e5604064 - Info     ResultsLocation: C:\Projects\Internal\EMOResults\results
17:27:18 - e5604064 - Info     requestDescription: 
17:27:18 - e5604064 - Info     No Step config
17:27:18 - e5604064 - Info     resultsLoadingFlag: yes
17:27:18 - e5604064 - Info     csvLoadingFlag: no
17:27:18 - e5604064 - Info     extractionFlag: no
17:27:18 - e5604064 - Info     update_t_AggPrjData: no
17:27:18 - e5604064 - Info     update_t_IMPData: no
17:27:18 - e5604064 - Info     update_t_PRJData: no
17:27:18 - e5604064 - Info     update_t_StoMData: no
17:27:18 - e5604064 - Info     update_t_StoYData: no
17:27:18 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
17:27:18 - e5604064 - Info     variable_filter: ''
17:27:18 - e5604064 - Step InfoCreating new load request
17:27:18 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
17:27:18 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "",
            "resultsLocation": "C:\\Projects\\Internal\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-04-30T17:27:18",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
17:27:25 - e5604064 - Info     <Response [201]>
17:27:25 - e5604064 - Info     Request was successful.
17:27:25 - e5604064 - Step Infoconnecting to Snowflake control database
17:27:26 - e5604064 - Info     connected to controldb_idr
17:27:31 - e5604064 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
17:27:31 - e5604064 - Error    Error: cannot unpack non-iterable NoneType object