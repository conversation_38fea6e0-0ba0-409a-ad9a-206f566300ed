
11:15:04 - e5604064 - Info     PROPHET_RESULTS_DIR not available
11:15:39 - e5604064 - Step Inforeading results settings
11:15:39 - e5604064 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results
11:15:39 - e5604064 - Info     requestDescription: localtest
11:15:39 - e5604064 - Info     No Step config
11:15:39 - e5604064 - Info     resultsLoadingFlag: yes
11:15:39 - e5604064 - Info     csvLoadingFlag: no
11:15:39 - e5604064 - Info     extractionFlag: no
11:15:39 - e5604064 - Info     update_t_AggPrjData: no
11:15:39 - e5604064 - Info     update_t_IMPData: no
11:15:39 - e5604064 - Info     update_t_PRJData: no
11:15:39 - e5604064 - Info     update_t_StoMData: no
11:15:39 - e5604064 - Info     update_t_StoYData: no
11:15:39 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
11:15:39 - e5604064 - Info     variable_filter: ''
11:15:39 - e5604064 - Step InfoCreating new load request
11:15:39 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
11:15:39 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "localtest",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-05-02T11:15:39",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
11:15:40 - e5604064 - Info     <Response [201]>
11:15:40 - e5604064 - Info     Request was successful.
11:15:40 - e5604064 - Step Infoconnecting to Snowflake control database
11:15:42 - e5604064 - Info     connected to controldb_idr
11:15:47 - e5604064 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
11:15:48 - e5604064 - Error    Error: cannot unpack non-iterable NoneType object
16:20:13 - e5604064 - Info     PROPHET_RESULTS_DIR not available
16:21:41 - e5604064 - Step Inforeading results settings
16:21:41 - e5604064 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results
16:21:41 - e5604064 - Info     requestDescription: test_snowflake
16:21:41 - e5604064 - Info     No Step config
16:21:41 - e5604064 - Info     resultsLoadingFlag: yes
16:21:41 - e5604064 - Info     csvLoadingFlag: no
16:21:41 - e5604064 - Info     extractionFlag: no
16:21:41 - e5604064 - Info     update_t_AggPrjData: no
16:21:41 - e5604064 - Info     update_t_IMPData: no
16:21:41 - e5604064 - Info     update_t_PRJData: no
16:21:41 - e5604064 - Info     update_t_StoMData: no
16:21:41 - e5604064 - Info     update_t_StoYData: no
16:21:41 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
16:21:41 - e5604064 - Info     variable_filter: ''
16:21:41 - e5604064 - Step InfoCreating new load request
16:21:41 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
16:21:41 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "test_snowflake",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-05-02T16:21:41",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
16:21:44 - e5604064 - Info     <Response [201]>
16:21:44 - e5604064 - Info     Request was successful.
16:21:44 - e5604064 - Step Infoconnecting to Snowflake control database
16:21:45 - e5604064 - Info     connected to controldb_idr
16:21:50 - e5604064 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
16:21:50 - e5604064 - Error    Error: cannot unpack non-iterable NoneType object