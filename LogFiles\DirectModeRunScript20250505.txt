
10:33:30 - e5604064 - Info     PROPHET_RESULTS_DIR not available
10:34:24 - e5604064 - Step Inforeading results settings
10:34:24 - e5604064 - Info     ResultsLocation: \\vwmazpswkspace\Data\Projects\Internal\BiProjects\PyCodes\EMOResults\results
10:34:24 - e5604064 - Info     requestDescription: test_snowflake_20250505
10:34:24 - e5604064 - Info     No Step config
10:34:24 - e5604064 - Info     resultsLoadingFlag: yes
10:34:24 - e5604064 - Info     csvLoadingFlag: no
10:34:24 - e5604064 - Info     extractionFlag: no
10:34:24 - e5604064 - Info     update_t_AggPrjData: no
10:34:24 - e5604064 - Info     update_t_IMPData: no
10:34:24 - e5604064 - Info     update_t_PRJData: no
10:34:24 - e5604064 - Info     update_t_StoMData: no
10:34:24 - e5604064 - Info     update_t_StoYData: no
10:34:24 - e5604064 - Info     requestFilters: "requestFilters": {
    
            }
10:34:24 - e5604064 - Info     variable_filter: ''
10:34:24 - e5604064 - Step InfoCreating new load request
10:34:24 - e5604064 - Info     requestUrl: http://vwmazidr2023q2.fisdev.local:100/api/v1/requests
10:34:24 - e5604064 - info     request body: {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "test_snowflake_20250505",
            "resultsLocation": "\\\\vwmazpswkspace\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\EMOResults\\results",
            "maxInstructions": 20,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2025-05-05T10:34:24",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
    
            }
        }
    }
}
10:34:27 - e5604064 - Info     <Response [201]>
10:34:27 - e5604064 - Info     Request was successful.
10:34:27 - e5604064 - Step Infoconnecting to Snowflake control database
10:34:28 - e5604064 - Info     connected to controldb_idr
10:34:33 - e5604064 - Info     checkRequestStatusQuery: SELECT requestid, requestdescription, statusid FROM idr.request ORDER BY 1 DESC LIMIT 1
10:34:34 - e5604064 - Error    Error: cannot unpack non-iterable NoneType object