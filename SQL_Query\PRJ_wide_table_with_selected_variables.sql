
select split_part(rowid, '&', 1) AS requestid,
    split_part(rowid, '&', 2) AS resultslocation,
    split_part(rowid, '&', 3) AS requestDescription,
	split_part(rowid, '&', 4) AS resultssetid,
	split_part(rowid, '&', 5) AS resultssetpieceid,
	split_part(rowid, '&', 6) AS runnumber,
	split_part(rowid, '&', 7) AS product,
	split_part(rowid, '&', 8) AS spcode,
	split_part(rowid, '&', 9) AS monthid,
    START_SVFP_A,
    START_SVFP_B,
    START_SVFP_C
	from crosstab($$SELECT
	r1.requestid::TEXT||'&'||r1.resultslocation||'&'||r1.requestDescription||'&'||r1.resultssetid::TEXT||'&'||r2.resultssetpieceid::TEXT||'&'||
	r1.runnumber::TEXT||'&'||r1.productname||'&'|| (r2.resultssetpiecekey ->> 'spcode'::text) ||'&'||r6.key as rowid,
    r1.variablename as variable,
    COALESCE(r6.value ->> ((r1."ValueArray" -> 'VariablePlacementKey'::text)::text)
	    , r2.constants ->> ((r1."ValueArray" -> 'VariablePlacementKey'::text)::text)) AS "value"
	
	FROM (SELECT (((((((('{ "VariablePlacementKey" :'::text || max(v1.key)) || ','::text) || '"VariableInfoKey" : '::text) || max(v2.key)) || ','::text) || '"VariableInfo" : '::text) || max(v2.variableinfo)) || '}'::text)::jsonb AS "ValueArray",
			v1.requestid,
			v1.resultslocation,
			v1.requestDescription,
			v1.resultssetid,
			v1.key,
			v1.variablename,
			v2.key AS key1,
			v2.variableinfo,
			v1.productname,
			v1.runnumber,
			v1.resultsclass
			FROM (SELECT 
					r0.requestid,
					r0.resultslocation,
					r0.requestDescription,
					r1_1.resultssetid,
					r1_1.resultssetkey,
					r2_1.key,
					r2_1.value AS variablename,
					r1_1.resultssetkey ->> 'product'::text AS productname,
					r1_1.resultssetkey ->> 'runnumber'::text AS runnumber,
					r1_1.resultssetkey ->> 'resultsclass'::text AS resultsclass
                 FROM idr.request r0
                 JOIN idr.resultsset r1_1 ON r0.requestid = r1_1.requestid
                 CROSS JOIN LATERAL jsonb_each_text(r1_1.variables) r2_1(key, value)
                 WHERE (r1_1.resultssetkey ->> 'resultsclass'::text) = 'Prj'::text AND r0.requestid = 54
				 AND r2_1.value::text in ('START_SVFP_A','START_SVFP_B','START_SVFP_C')) v1
            LEFT JOIN (SELECT 
					r0.requestid,
                    r1_1.resultssetid,
                    r1_1.resultssetkey,
                    r2_1.key,
                    r2_1.value::jsonb ->> 'name'::text AS variablename,
                    r2_1.value AS variableinfo
                 FROM idr.request r0
                 JOIN idr.resultsset r1_1 ON r0.requestid = r1_1.requestid
                 CROSS JOIN LATERAL jsonb_each_text(r1_1.variableinfo) r2_1(key, value)
                 WHERE (r1_1.resultssetkey ->> 'resultsclass'::text) = 'Prj'::text AND r0.requestid = 54) v2 
			ON v1.requestid = v2.requestid AND v1.resultssetid = v2.resultssetid AND v1.variablename = v2.variablename
			GROUP BY v1.requestid,v1.resultslocation,v1.requestDescription, v1.resultssetid, v1.resultssetkey, v1.key, v1.variablename, v2.requestid, v2.key, v2.variableinfo, v1.productname, v1.runnumber, v1.resultsclass
						) r1
    JOIN idr.resultssetpiece r2 ON r1.resultssetid = r2.resultssetid
    CROSS JOIN LATERAL jsonb_each(r2.results) r6(key, value)
	WHERE ((SELECT jsonb_each.key
			FROM jsonb_each(r2.nonconstantmap) jsonb_each(key, value)
			WHERE jsonb_each.value::integer = (r1."ValueArray" -> 'VariablePlacementKey'::text)::integer)) IS NOT NULL
			order by r1.requestid,r1.resultslocation,r1.requestDescription,r1.resultssetid,r2.resultssetpieceid,
	r1.runnumber,r1.productname, r2.resultssetpiecekey ->> 'spcode' ,r6.key, r1.variablename$$
					   
	) AS ct (rowid text,START_SVFP_A text,START_SVFP_B text,START_SVFP_C text)