

CREATE OR REPLACE VIEW yournamehere.vresultsimpoffset
 AS
 WITH qv01 AS (
         SELECT rs.requestid,
            rs.resultssetid,
            rs.resultssetkey,
            rs.resultstype,
            rsv.key::integer AS varindex,
            rsv.value AS varname
           FROM yournamehere.resultsset rs
             CROSS JOIN LATERAL jsonb_each_text(rs.variables) rsv(key, value)
          WHERE rs.resultstype::text = 'ImpOffset'::text
        ), qr01 AS (
         SELECT rsp.resultssetid,
            rsp.resultssetpieceid,
            rsp.resultssetpiecekey,
            rsf.resultssetfragmentid,
            rspr.key::integer AS modelpoint,
            rspr.value::jsonb AS results1
           FROM yournamehere.resultssetpiece rsp,
            yournamehere.resultssetfragment rsf
             CROSS JOIN LATERAL jsonb_each_text(rsf.results) rspr(key, value)
          WHERE rsp.resultssetpieceid = rsf.resultssetpieceid
        ), qr02 AS (
         SELECT qr01.resultssetid,
            qr01.resultssetpieceid,
            qr01.resultssetpiecekey,
            qr01.resultssetfragmentid,
            qr01.modelpoint,
            rsprv.key::integer AS t,
            rsprv.value::jsonb AS results2
           FROM qr01
             CROSS JOIN LATERAL jsonb_each_text(qr01.results1) rsprv(key, value)
        ), qr03 AS (
         SELECT qr02.resultssetid,
            qr02.resultssetpieceid,
            qr02.resultssetpiecekey,
            qr02.resultssetfragmentid,
            qr02.modelpoint,
            qr02.t,
            rsprv1.key::integer AS varindex,
            rsprv1.value
           FROM qr02
             CROSS JOIN LATERAL jsonb_each_text(qr02.results2) rsprv1(key, value)
        ),qrc01 AS (
         SELECT rsp.resultssetid,
            rsp.resultssetpieceid,
            rsp.resultssetpiecekey,
            rsf.resultssetfragmentid,
            NULL::integer AS modelpoint,
            NULL::integer AS t,
            rspr.key::integer AS varindex,
            rspr.value
           FROM yournamehere.resultssetpiece rsp,
            yournamehere.resultssetfragment rsf
             CROSS JOIN LATERAL jsonb_each_text(rsf.constants) rspr(key, value)
          WHERE rsp.resultssetpieceid = rsf.resultssetpieceid
        ), qr05 AS (
         SELECT qr03.resultssetid,
            qr03.resultssetpieceid,
            qr03.resultssetpiecekey,
            qr03.resultssetfragmentid,
            qr03.modelpoint,
            qr03.t,
            qr03.varindex,
            qr03.value,
            'V'::text AS indicator
           FROM qr03
        UNION ALL
         SELECT qrc01.resultssetid,
            qrc01.resultssetpieceid,
            qrc01.resultssetpiecekey,
            qrc01.resultssetfragmentid,
            qrc01.modelpoint,
            qrc01.t,
            qrc01.varindex,
            qrc01.value,
            'C'::text AS text
           FROM qrc01
        )
 SELECT qv01.requestid,
    qr05.resultssetid,
    qv01.resultstype,
    qr05.resultssetpieceid,
    qr05.resultssetfragmentid,
    qr05.resultssetpiecekey ->> 'runnumber'::text AS runnumber,
    qr05.resultssetpiecekey ->> 'product'::text AS product,
    qr05.resultssetpiecekey ->> 'spcode'::text AS spcode,
    qr05.modelpoint,
    qr05.t,
    qv01.varindex,
    qv01.varname,
    qr05.indicator,
    qr05.value
   FROM qr05
     JOIN qv01 ON qv01.varindex = qr05.varindex AND qv01.resultssetid = qr05.resultssetid;

ALTER TABLE yournamehere.vresultsimpoffset
    OWNER TO yournamehereuser;

