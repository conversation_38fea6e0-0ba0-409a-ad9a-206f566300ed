
CREATE OR REPLACE VIEW yournamehere.vresultspproj
 AS
 WITH qv01 AS (
         SELECT rs.requestid,
            rs.resultssetid,
            rs.resultssetkey,
            rs.resultstype,
            rsv.key::integer AS varindex,
            rsv.value AS varname
           FROM yournamehere.resultsset rs
             CROSS JOIN LATERAL jsonb_each_text(rs.variables) rsv(key, value)
          WHERE rs.resultstype::text = 'PProj'::text
        ), qr01 AS (
         SELECT rsp.resultssetid,
            rsp.resultssetpieceid,
            rsp.resultssetpiecekey,
            rspr.key::integer AS t,
            rspr.value::jsonb AS d
           FROM yournamehere.resultssetpiece rsp
             CROSS JOIN LATERAL jsonb_each_text(rsp.results) rspr(key, value)
        ), qr02 AS (
         SELECT qr01.resultssetid,
            qr01.resultssetpieceid,
            qr01.resultssetpiecekey,
            qr01.t,
            rsprv.key::integer AS varindex,
            rsprv.value
           FROM qr01
             CROSS JOIN LATERAL jsonb_each_text(qr01.d) rsprv(key, value)
        ), qr03 AS (
         SELECT rsp.resultssetid,
            rsp.resultssetpieceid,
            rsp.resultssetpiecekey,
            NULL::integer AS t,
            rspc.key::integer AS varindex,
            rspc.value
           FROM yournamehere.resultssetpiece rsp
             CROSS JOIN LATERAL jsonb_each_text(rsp.constants) rspc(key, value)
        ), qr05 AS (
         SELECT qr02.resultssetid,
            qr02.resultssetpieceid,
            qr02.resultssetpiecekey,
            qr02.t,
            qr02.varindex,
            qr02.value,
            'V'::text AS indicator
           FROM qr02
        UNION ALL
         SELECT qr03.resultssetid,
            qr03.resultssetpieceid,
            qr03.resultssetpiecekey,
            qr03.t,
            qr03.varindex,
            qr03.value,
            'C'::text AS text
           FROM qr03
        )
 SELECT qv01.requestid,
    qr05.resultssetid,
    qv01.resultstype,
    qr05.resultssetpieceid,
    qr05.resultssetpiecekey ->> 'runnumber'::text AS runnumber,
    qr05.resultssetpiecekey ->> 'product'::text AS product,
    qr05.resultssetpiecekey ->> 'policyidentifier'::text AS policyidentifier,
    qr05.t,
    qv01.varindex,
    qv01.varname,
    qr05.indicator,
    qr05.value
   FROM qr05
     JOIN qv01 ON qv01.varindex = qr05.varindex AND qv01.resultssetid = qr05.resultssetid;

ALTER TABLE yournamehere.vresultspproj
    OWNER TO yournamehereuser;

