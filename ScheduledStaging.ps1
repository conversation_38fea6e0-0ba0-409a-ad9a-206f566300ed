#Requires -Version 5.0

<#
.SYNOPSIS
    Scheduled staging script that processes files based on trigger file presence
    
.DESCRIPTION
    This script checks for a trigger file in the configured source directory.
    If the trigger file exists, it moves the trigger file to a TriggerProcessed subdirectory
    and continues with the staging logic. Otherwise, it exits.
    
.PARAMETER ConfigFile
    Path to the configuration file (JSON format)
    
.PARAMETER LogPath
    Path to write log files
    
.PARAMETER TriggerFileName
    Name of the trigger file to look for (default: STAGING_TRIGGER.txt)
    
.PARAMETER TestMode
    Run in test mode without actually processing files
    
.EXAMPLE
    .\ScheduledStaging.ps1 -ConfigFile "C:\Config\staging.json"
    
.EXAMPLE
    .\ScheduledStaging.ps1 -ConfigFile "C:\Config\staging.json" -TestMode
#>

param(
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = "C:\Config\StagingConfig.json",
    
    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\Logs\ScheduledStaging",
    
    [Parameter(Mandatory=$false)]
    [string]$TriggerFileName = "STAGING_TRIGGER.txt",
    
    [Parameter(Mandatory=$false)]
    [switch]$TestMode
)

# Initialize logging
if (-not (Test-Path $LogPath)) {
    New-Item -Path $LogPath -ItemType Directory -Force | Out-Null
}

$LogFile = Join-Path $LogPath "ScheduledStaging_$(Get-Date -Format 'yyyyMMdd').log"

function Write-Log {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "DEBUG", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    
    # Write to console and log file
    Write-Output $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry -Encoding UTF8
}

function Load-Configuration {
    param([string]$ConfigPath)
    
    try {
        if (-not (Test-Path $ConfigPath)) {
            # Create default configuration if it doesn't exist
            Write-Log "Configuration file not found. Creating default configuration: $ConfigPath" "WARN"
            $DefaultConfig = @{
                SourceDirectory = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\"
                DestinationDirectory = "\\MDTMNASPAWFS001\PPData$\2_TEST\InputFiles\Entities\TMA\Load\"
                ArchiveDirectory = "\\MDTMNASPAWFS001\PPData$\2_TEST\InputFiles\Entities\TMA\Processed\"
                TriggerProcessedDirectory = "TriggerProcessed"  # Relative to SourceDirectory
                FilePatterns = @("*.csv", "*.xml", "*.txt")
                RequireFileForSuccess = $true
                MaxRetries = 3
                RetryDelaySeconds = 30
            }
            
            # Ensure config directory exists
            $ConfigDir = Split-Path $ConfigPath -Parent
            if (-not (Test-Path $ConfigDir)) {
                New-Item -Path $ConfigDir -ItemType Directory -Force | Out-Null
            }
            
            $DefaultConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $ConfigPath -Encoding UTF8
            Write-Log "Default configuration created: $ConfigPath"
        }
        
        $ConfigContent = Get-Content -Path $ConfigPath -Raw
        $Config = $ConfigContent | ConvertFrom-Json
        
        Write-Log "Configuration loaded successfully from: $ConfigPath"
        return $Config
        
    } catch {
        Write-Log "Error loading configuration: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Test-NetworkPath {
    param([string]$Path)
    
    try {
        if (Test-Path $Path -ErrorAction Stop) {
            return $true
        }
    } catch {
        Write-Log "Cannot access path: $Path - $($_.Exception.Message)" "ERROR"
        return $false
    }
    return $false
}

function Check-TriggerFile {
    param(
        [string]$SourceDirectory,
        [string]$TriggerFileName,
        [string]$TriggerProcessedSubDir
    )
    
    try {
        # Check if source directory exists
        if (-not (Test-NetworkPath $SourceDirectory)) {
            Write-Log "Source directory does not exist or is not accessible: $SourceDirectory" "ERROR"
            return @{
                Success = $false
                TriggerFound = $false
                Message = "Source directory not accessible"
            }
        }
        
        Write-Log "Source directory exists and is accessible: $SourceDirectory"
        
        # Look for trigger file in source directory
        $TriggerFilePath = Join-Path $SourceDirectory $TriggerFileName
        
        if (-not (Test-Path $TriggerFilePath)) {
            Write-Log "No trigger file found: $TriggerFilePath" "DEBUG"
            return @{
                Success = $true
                TriggerFound = $false
                Message = "No trigger file found"
            }
        }
        
        Write-Log "Trigger file found: $TriggerFilePath" "INFO"
        
        # Create TriggerProcessed subdirectory if it doesn't exist
        $TriggerProcessedPath = Join-Path $SourceDirectory $TriggerProcessedSubDir
        if (-not (Test-Path $TriggerProcessedPath)) {
            New-Item -Path $TriggerProcessedPath -ItemType Directory -Force | Out-Null
            Write-Log "Created TriggerProcessed directory: $TriggerProcessedPath"
        }
        
        # Read trigger file content for logging
        try {
            $TriggerContent = Get-Content -Path $TriggerFilePath -Raw -ErrorAction SilentlyContinue
            if ($TriggerContent) {
                Write-Log "Trigger file content: $($TriggerContent.Substring(0, [Math]::Min(200, $TriggerContent.Length)))"
            }
        } catch {
            Write-Log "Could not read trigger file content" "WARN"
        }
        
        # Move trigger file to TriggerProcessed subdirectory with timestamp
        $ProcessedFileName = "$(Get-Date -Format 'yyyyMMdd_HHmmss')_$TriggerFileName"
        $ProcessedFilePath = Join-Path $TriggerProcessedPath $ProcessedFileName
        
        Move-Item -Path $TriggerFilePath -Destination $ProcessedFilePath -Force
        Write-Log "Moved trigger file to: $ProcessedFilePath" "SUCCESS"
        
        return @{
            Success = $true
            TriggerFound = $true
            ProcessedPath = $ProcessedFilePath
            Message = "Trigger file processed successfully"
        }
        
    } catch {
        Write-Log "Error checking trigger file: $($_.Exception.Message)" "ERROR"
        return @{
            Success = $false
            TriggerFound = $false
            Message = $_.Exception.Message
        }
    }
}

function Invoke-StagingFiles {
    param(
        [object]$Config,
        [bool]$TestMode = $false
    )

    $FilesProcessed = 0
    $Errors = @()

    try {
        Write-Log "Starting staging file processing..."
        Write-Log "Source Directory: $($Config.SourceDirectory)"
        Write-Log "Destination Directory: $($Config.DestinationDirectory)"
        Write-Log "Archive Directory: $($Config.ArchiveDirectory)"

        if ($TestMode) {
            Write-Log "Running in TEST MODE - no files will be moved" "WARN"
        }

        # Validate all required directories
        $RequiredPaths = @(
            $Config.SourceDirectory,
            $Config.DestinationDirectory,
            $Config.ArchiveDirectory
        )

        foreach ($Path in $RequiredPaths) {
            if (-not (Test-NetworkPath $Path)) {
                throw "Required path not accessible: $Path"
            }
        }

        # Ensure destination and archive directories exist
        if (-not $TestMode) {
            if (-not (Test-Path $Config.DestinationDirectory)) {
                New-Item -Path $Config.DestinationDirectory -ItemType Directory -Force | Out-Null
                Write-Log "Created destination directory: $($Config.DestinationDirectory)"
            }

            if (-not (Test-Path $Config.ArchiveDirectory)) {
                New-Item -Path $Config.ArchiveDirectory -ItemType Directory -Force | Out-Null
                Write-Log "Created archive directory: $($Config.ArchiveDirectory)"
            }
        }

        # Process each file pattern
        foreach ($Pattern in $Config.FilePatterns) {
            $SourcePattern = Join-Path $Config.SourceDirectory $Pattern
            $Files = Get-ChildItem -Path $SourcePattern -File -ErrorAction SilentlyContinue

            Write-Log "Found $($Files.Count) files matching pattern: $Pattern"

            foreach ($File in $Files) {
                try {
                    $DestPath = Join-Path $Config.DestinationDirectory $File.Name
                    $ArchivePath = Join-Path $Config.ArchiveDirectory $File.Name

                    if ($TestMode) {
                        Write-Log "TEST MODE: Would process file: $($File.Name)"
                        Write-Log "TEST MODE: Would copy to destination: $DestPath"
                        Write-Log "TEST MODE: Would copy to archive: $ArchivePath"
                        Write-Log "TEST MODE: Would remove from source: $($File.FullName)"
                    } else {
                        # Copy to destination
                        Copy-Item -Path $File.FullName -Destination $DestPath -Force
                        Write-Log "Copied: $($File.Name) -> Destination"

                        # Copy to archive
                        Copy-Item -Path $File.FullName -Destination $ArchivePath -Force
                        Write-Log "Copied: $($File.Name) -> Archive"

                        # Remove from source (only after successful copies)
                        Remove-Item -Path $File.FullName -Force
                        Write-Log "Removed from source: $($File.Name)"
                    }

                    $FilesProcessed++

                } catch {
                    $ErrorMsg = "Failed to process file $($File.Name): $($_.Exception.Message)"
                    Write-Log $ErrorMsg "ERROR"
                    $Errors += $ErrorMsg
                }
            }
        }

        Write-Log "File processing completed. Files processed: $FilesProcessed"

        if ($Errors.Count -gt 0) {
            Write-Log "Errors encountered during processing:" "WARN"
            foreach ($ErrorMsg in $Errors) {
                Write-Log $ErrorMsg "ERROR"
            }
        }

        # Check if files were required for success
        if ($Config.RequireFileForSuccess -and $FilesProcessed -eq 0 -and -not $TestMode) {
            Write-Log "No files were processed but files are required for success" "ERROR"
            return @{
                Success = $false
                FilesProcessed = $FilesProcessed
                Errors = $Errors
                Message = "No files processed but files required for success"
            }
        }

        return @{
            Success = $true
            FilesProcessed = $FilesProcessed
            Errors = $Errors
            Message = "File processing completed successfully"
        }

    } catch {
        $ErrorMsg = "Error in file processing: $($_.Exception.Message)"
        Write-Log $ErrorMsg "ERROR"
        return @{
            Success = $false
            FilesProcessed = $FilesProcessed
            Errors = $Errors + $ErrorMsg
            Message = $ErrorMsg
        }
    }
}

function Remove-OldLogs {
    param([string]$LogDirectory, [int]$DaysToKeep = 30)

    try {
        $CutoffDate = (Get-Date).AddDays(-$DaysToKeep)
        $OldLogs = Get-ChildItem -Path $LogDirectory -Filter "*.log" | Where-Object { $_.LastWriteTime -lt $CutoffDate }

        foreach ($Log in $OldLogs) {
            Remove-Item -Path $Log.FullName -Force
            Write-Log "Removed old log: $($Log.Name)" "DEBUG"
        }
    } catch {
        Write-Log "Error cleaning old logs: $($_.Exception.Message)" "WARN"
    }
}

# Main execution
try {
    Write-Log "=== Starting ScheduledStaging.ps1 ===" "INFO"
    Write-Log "Configuration File: $ConfigFile"
    Write-Log "Log Path: $LogPath"
    Write-Log "Trigger File Name: $TriggerFileName"

    if ($TestMode) {
        Write-Log "Running in TEST MODE" "WARN"
    }

    # Clean old logs
    Remove-OldLogs -LogDirectory $LogPath -DaysToKeep 30

    # Load configuration
    Write-Log "Loading configuration..."
    $Config = Load-Configuration -ConfigPath $ConfigFile

    # Display configuration
    Write-Log "Configuration loaded:"
    Write-Log "  Source Directory: $($Config.SourceDirectory)"
    Write-Log "  Destination Directory: $($Config.DestinationDirectory)"
    Write-Log "  Archive Directory: $($Config.ArchiveDirectory)"
    Write-Log "  Trigger Processed Directory: $($Config.TriggerProcessedDirectory)"
    Write-Log "  File Patterns: $($Config.FilePatterns -join ', ')"
    Write-Log "  Require File For Success: $($Config.RequireFileForSuccess)"

    # Check for trigger file in source directory
    Write-Log "Checking for trigger file..."
    $TriggerResult = Check-TriggerFile -SourceDirectory $Config.SourceDirectory -TriggerFileName $TriggerFileName -TriggerProcessedSubDir $Config.TriggerProcessedDirectory

    if (-not $TriggerResult.Success) {
        Write-Log "Failed to check trigger file: $($TriggerResult.Message)" "ERROR"
        exit 1
    }

    if (-not $TriggerResult.TriggerFound) {
        Write-Log "No trigger file found. Exiting without processing." "INFO"
        Write-Log "=== ScheduledStaging.ps1 Completed (No Trigger) ===" "INFO"
        exit 0
    }

    Write-Log "Trigger file found and processed. Continuing with staging logic..." "SUCCESS"

    # Process staging files
    Write-Log "Starting file staging process..."
    $StagingResult = Invoke-StagingFiles -Config $Config -TestMode $TestMode

    if ($StagingResult.Success) {
        Write-Log "Staging completed successfully" "SUCCESS"
        Write-Log "Files processed: $($StagingResult.FilesProcessed)"

        if ($StagingResult.Errors.Count -gt 0) {
            Write-Log "Staging completed with some errors:" "WARN"
            foreach ($ErrorMsg in $StagingResult.Errors) {
                Write-Log "  $ErrorMsg" "WARN"
            }
        }
    } else {
        Write-Log "Staging failed: $($StagingResult.Message)" "ERROR"
        if ($StagingResult.Errors.Count -gt 0) {
            Write-Log "Staging errors:" "ERROR"
            foreach ($ErrorMsg in $StagingResult.Errors) {
                Write-Log "  $ErrorMsg" "ERROR"
            }
        }
        exit 1
    }

    Write-Log "=== ScheduledStaging.ps1 Completed Successfully ===" "SUCCESS"
    exit 0

} catch {
    Write-Log "Unexpected error in main execution: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}
