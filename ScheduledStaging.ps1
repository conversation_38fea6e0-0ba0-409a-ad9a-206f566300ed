param(
[string]$ResultsDirectory,
[string]$JobDirectory
)

try
{
    $ScriptDirectory = (Split-Path $script:MyInvocation.MyCommand.Path) + "\"
    Set-Location $ScriptDirectory
    Import-Module -Name ".\Integration.psm1" -Force

    $ResultsDirectory = ""
    $JobDirectory = ""

    # Load the XML file directly using PowerShell's built-in XML handling
    $xmlFilePath = "$ScriptDirectory\IntegrationTEST.xml"
    [xml]$xmlConfig = Get-Content -Path $xmlFilePath
    
    # Use XPath to find the PullSFTPFiles step and get its DestinationDirectory attribute
    $pullSFTPFilesNode = $xmlConfig.SelectSingleNode("//Step[@Name='PullSFTPFiles'][not(@Disabled=""True"")]")

    if ($pullSFTPFilesNode -ne $null) {
        $destinationPath = $pullSFTPFilesNode.GetAttribute("DestinationDirectory")
        Write-Host "Found destination directory: $destinationPath"
    } else {
        $destinationPath = "\\MDTMNASPAWFS001\PPData$\2_TEST\InputFiles\Entities\TMA\Load\"
        Write-Warning "Could not find PullSFTPFiles step in XML. Using default path: $destinationPath"
    }

    # Check for trigger file in SourceDirectory
    # Get the source directory from XML configuration
    $pullSFTPFilesSourceNode = $xmlConfig.SelectSingleNode("//Step[@Name='PullSFTPFiles'][not(@Disabled=""True"")]")
    
    if ($pullSFTPFilesSourceNode -ne $null) {
        $sourceDirectory = $pullSFTPFilesSourceNode.GetAttribute("SourceDirectory")
        if (-not $sourceDirectory) {
            # Try alternative attribute names that might be used in the XML
            $sourceDirectory = $pullSFTPFilesSourceNode.GetAttribute("Source")
            if (-not $sourceDirectory) {
                $sourceDirectory = $pullSFTPFilesSourceNode.GetAttribute("InputDirectory")
            }
        }
        Write-Host "Found source directory: $sourceDirectory"
    } else {
        $sourceDirectory = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\"
        Write-Warning "Could not find PullSFTPFiles source directory in XML. Using default path: $sourceDirectory"
    }

    # Define trigger file name and processed directory
    $triggerFileName = "STAGING_TRIGGER.txt"
    $triggerFilePath = Join-Path $sourceDirectory $triggerFileName
    $triggerProcessedDir = Join-Path $sourceDirectory "TriggerProcessed"

    # Check if source directory exists
    if (-not (Test-Path $sourceDirectory)) {
        Write-Error "Source directory does not exist or is not accessible: $sourceDirectory"
        WriteToLog -Logstring "ERROR: Source directory not accessible: $sourceDirectory"
        WriteToLog -Logstring "-------------------------------------------"
        exit 1
    }

    Write-Host "Checking for trigger file: $triggerFilePath"
    WriteToLog -Logstring "Checking for trigger file: $triggerFilePath"

    # Check if trigger file exists
    if (-not (Test-Path $triggerFilePath)) {
        Write-Host "No trigger file found. Exiting without processing."
        WriteToLog -Logstring "No trigger file found. Exiting without processing."
        WriteToLog -Logstring "-------------------------------------------"
        exit 0
    }

    Write-Host "Trigger file found: $triggerFilePath"
    WriteToLog -Logstring "Trigger file found: $triggerFilePath"

    # Create TriggerProcessed subdirectory if it doesn't exist
    if (-not (Test-Path $triggerProcessedDir)) {
        New-Item -Path $triggerProcessedDir -ItemType Directory -Force | Out-Null
        Write-Host "Created TriggerProcessed directory: $triggerProcessedDir"
        WriteToLog -Logstring "Created TriggerProcessed directory: $triggerProcessedDir"
    }

    # Read trigger file content for logging
    try {
        $triggerContent = Get-Content -Path $triggerFilePath -Raw -ErrorAction SilentlyContinue
        if ($triggerContent) {
            $contentPreview = $triggerContent.Substring(0, [Math]::Min(200, $triggerContent.Length))
            WriteToLog -Logstring "Trigger file content preview: $contentPreview"
        }
    } catch {
        WriteToLog -Logstring "Could not read trigger file content"
    }

    # Move trigger file to TriggerProcessed subdirectory with timestamp
    $processedFileName = "$(Get-Date -Format 'yyyyMMdd_HHmmss')_$triggerFileName"
    $processedFilePath = Join-Path $triggerProcessedDir $processedFileName

    try {
        Move-Item -Path $triggerFilePath -Destination $processedFilePath -Force
        Write-Host "Moved trigger file to: $processedFilePath"
        WriteToLog -Logstring "Moved trigger file to: $processedFilePath"
        WriteToLog -Logstring "Continuing with staging process..."
    } catch {
        Write-Error "Failed to move trigger file: $($_.Exception.Message)"
        WriteToLog -Logstring "ERROR: Failed to move trigger file: $($_.Exception.Message)"
        WriteToLog -Logstring "-------------------------------------------"
        exit 1
    }

    $RunSettings = Get-RunSettings
    $RunSettings.Domain = "TEST"
    $RunSettings.JobCollectionParent = "IFRS17"
    $RunSettings.EntityName = "TMA"
    $RunSettings.ReportingCycleName = ""
    $RunSettings.LoadPaths()

    # Execute the PullFiles job collection
    $RunSettings.JobCollectionContextCode = "PullFiles"
    ProcessJobCollectionActions -RunSettings $RunSettings -SuppressResultsLock $true

    # Check if files were moved to the destination directory
    $files = Get-ChildItem -Path $destinationPath -File -ErrorAction SilentlyContinue
    
    # Log the results
    if ($files.Count -eq 0) {
        Write-Warning "No files found in destination folder: $destinationPath"
        WriteToLog -Logstring "No files found in destination folder after PullFiles job"
        WriteToLog -Logstring "-------------------------------------------"
    } else {
        Write-Host "Found $($files.Count) files in destination folder"
        WriteToLog -Logstring "Found $($files.Count) files in destination folder after PullFiles job"
        WriteToLog -Logstring "-------------------------------------------"

        # Continue with the Staging job collection when files are found in the destination folder    
        $RunSettings.JobCollectionContextCode = "Staging"
        ProcessJobCollectionActions -RunSettings $RunSettings -SuppressResultsLock $true
    }

}
catch 
{
    $LocalError = $_.Exception
    Add-Content -Value $LocalError -Path "\\MDTMNASPAWFS001\PPData$\2_TEST\ProphetScripts\UserFiles\ScheduledStaging.txt"
    WriteToLog -Logstring "ERROR: $LocalError"
    WriteToLog -Logstring "-------------------------------------------"
}
