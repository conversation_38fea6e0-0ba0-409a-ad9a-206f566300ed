#Requires -Version 5.0
#Requires -RunAsAdministrator

<#
.SY<PERSON><PERSON><PERSON><PERSON>
    Creates a Windows Task Scheduler task to run the TriggerStagingMonitor script
    
.DESCRIPTION
    Sets up a scheduled task that runs the TriggerStagingMonitor.ps1 script periodically
    to monitor for trigger files and execute ScheduledStaging.ps1 when found.
    
.PARAMETER TaskName
    Name of the scheduled task
    
.PARAMETER MonitorScriptPath
    Full path to the TriggerStagingMonitor.ps1 script
    
.PARAMETER StagingScriptPath
    Full path to the ScheduledStaging.ps1 script
    
.PARAMETER RunAsUser
    User account to run the task as (default: current user)
    
.PARAMETER IntervalMinutes
    How often to run the task in minutes (default: 2)
    
.PARAMETER LogPath
    Path for log files
    
.PARAMETER TimeoutMinutes
    Timeout for ScheduledStaging.ps1 execution
    
.EXAMPLE
    .\Setup-StagingMonitorTask.ps1 -TaskName "StagingMonitor" -MonitorScriptPath "C:\Scripts\TriggerStagingMonitor.ps1" -StagingScriptPath "C:\Scripts\ScheduledStaging.ps1"
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$TaskName,
    
    [Parameter(Mandatory=$true)]
    [string]$MonitorScriptPath,
    
    [Parameter(Mandatory=$true)]
    [string]$StagingScriptPath,
    
    [Parameter(Mandatory=$false)]
    [string]$RunAsUser = $env:USERNAME,
    
    [Parameter(Mandatory=$false)]
    [int]$IntervalMinutes = 2,
    
    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\Logs\TriggerStagingMonitor",
    
    [Parameter(Mandatory=$false)]
    [int]$TimeoutMinutes = 60,
    
    [Parameter(Mandatory=$false)]
    [string]$TriggerFile = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\STAGING_TRIGGER.txt"
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Output "[$Timestamp] [$Level] $Message"
}

function Register-EventSource {
    param([string]$SourceName)
    
    try {
        if (-not [System.Diagnostics.EventLog]::SourceExists($SourceName)) {
            New-EventLog -LogName Application -Source $SourceName
            Write-Log "Created event log source: $SourceName"
        }
    } catch {
        Write-Log "Could not create event log source: $($_.Exception.Message)" "WARN"
    }
}

try {
    Write-Log "Setting up scheduled task: $TaskName"
    
    # Verify scripts exist
    if (-not (Test-Path $MonitorScriptPath)) {
        throw "Monitor script not found: $MonitorScriptPath"
    }
    
    if (-not (Test-Path $StagingScriptPath)) {
        throw "Staging script not found: $StagingScriptPath"
    }
    
    # Create log directory if it doesn't exist
    if (-not (Test-Path $LogPath)) {
        New-Item -Path $LogPath -ItemType Directory -Force | Out-Null
        Write-Log "Created log directory: $LogPath"
    }
    
    # Register event log source
    Register-EventSource -SourceName "TriggerStagingMonitor"
    
    # Remove existing task if it exists
    $ExistingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
    if ($ExistingTask) {
        Write-Log "Removing existing task: $TaskName"
        Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
    }
    
    # Build PowerShell command arguments
    $PowerShellArgs = @(
        "-ExecutionPolicy Bypass"
        "-NoProfile"
        "-WindowStyle Hidden"
        "-File `"$MonitorScriptPath`""
        "-TriggerFile `"$TriggerFile`""
        "-StagingScript `"$StagingScriptPath`""
        "-LogPath `"$LogPath`""
        "-TimeoutMinutes $TimeoutMinutes"
    )
    
    $PowerShellCommand = $PowerShellArgs -join " "
    
    Write-Log "PowerShell command: PowerShell.exe $PowerShellCommand"
    
    # Create scheduled task action
    $Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument $PowerShellCommand
    
    # Create trigger to run every X minutes
    $Trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes $IntervalMinutes) -RepetitionDuration (New-TimeSpan -Days 365)
    
    # Create task settings
    $Settings = New-ScheduledTaskSettingsSet `
        -AllowStartIfOnBatteries `
        -DontStopIfGoingOnBatteries `
        -StartWhenAvailable `
        -RunOnlyIfNetworkAvailable `
        -ExecutionTimeLimit (New-TimeSpan -Hours 2) `
        -RestartCount 3 `
        -RestartInterval (New-TimeSpan -Minutes 5)
    
    # Create principal (user context)
    if ($RunAsUser -eq $env:USERNAME) {
        Write-Log "Task will run as current user: $RunAsUser"
        $Principal = New-ScheduledTaskPrincipal -UserId $RunAsUser -LogonType Interactive -RunLevel Highest
        
        # Register the scheduled task
        Register-ScheduledTask -TaskName $TaskName -Action $Action -Trigger $Trigger -Settings $Settings -Principal $Principal
    } else {
        Write-Log "Task will run as: $RunAsUser"
        $Credential = Get-Credential -UserName $RunAsUser -Message "Enter password for $RunAsUser"
        
        # Register the scheduled task with credentials
        Register-ScheduledTask -TaskName $TaskName -Action $Action -Trigger $Trigger -Settings $Settings -User $RunAsUser -Password $Credential.GetNetworkCredential().Password -RunLevel Highest
    }
    
    Write-Log "Scheduled task created successfully: $TaskName"
    Write-Log "Task will run every $IntervalMinutes minutes"
    Write-Log "Monitor Script: $MonitorScriptPath"
    Write-Log "Staging Script: $StagingScriptPath"
    Write-Log "Trigger File: $TriggerFile"
    Write-Log "Log Path: $LogPath"
    Write-Log "Staging Timeout: $TimeoutMinutes minutes"
    
    # Display task information
    $Task = Get-ScheduledTask -TaskName $TaskName
    Write-Log "Task State: $($Task.State)"
    Write-Log "Task Principal: $($Task.Principal.UserId)"
    Write-Log "Task Triggers: $($Task.Triggers.Count)"
    
    # Test the task (optional)
    $TestTask = Read-Host "Do you want to test the task now? (y/n)"
    if ($TestTask -eq 'y' -or $TestTask -eq 'Y') {
        Write-Log "Testing the scheduled task..."
        Start-ScheduledTask -TaskName $TaskName
        
        Start-Sleep -Seconds 10
        
        $TaskInfo = Get-ScheduledTaskInfo -TaskName $TaskName
        Write-Log "Task last run time: $($TaskInfo.LastRunTime)"
        Write-Log "Task last result: $($TaskInfo.LastTaskResult)"
        
        if ($TaskInfo.LastTaskResult -eq 0) {
            Write-Log "Task test completed successfully" "SUCCESS"
        } else {
            Write-Log "Task test failed with result: $($TaskInfo.LastTaskResult)" "WARN"
            Write-Log "Check the log file for details: $LogPath"
        }
    }
    
    Write-Log "Setup completed successfully"
    Write-Log ""
    Write-Log "To create a trigger file and test the system, run:"
    Write-Log ".\Create-StagingTrigger.ps1 -TriggerFilePath `"$TriggerFile`""
    Write-Log ""
    Write-Log "To monitor the task:"
    Write-Log "Get-ScheduledTaskInfo -TaskName `"$TaskName`""
    Write-Log "Get-Content `"$LogPath\TriggerStagingMonitor_$(Get-Date -Format 'yyyyMMdd').log`" -Tail 20"
    
} catch {
    Write-Log "Error setting up scheduled task: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}
