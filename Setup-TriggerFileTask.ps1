#Requires -Version 5.0
#Requires -RunAsAdministrator

<#
.SY<PERSON><PERSON><PERSON><PERSON>
    Creates a Windows Task Scheduler task to run the TriggerFileProcessor script
    
.DESCRIPTION
    Sets up a scheduled task that runs the TriggerFileProcessor.ps1 script periodically
    to monitor for trigger files and process SFTP files.
    
.PARAMETER TaskName
    Name of the scheduled task
    
.PARAMETER ScriptPath
    Full path to the TriggerFileProcessor.ps1 script
    
.PARAMETER RunAsUser
    User account to run the task as (default: current user)
    
.PARAMETER IntervalMinutes
    How often to run the task in minutes (default: 5)
    
.PARAMETER LogPath
    Path for log files
    
.EXAMPLE
    .\Setup-TriggerFileTask.ps1 -TaskName "TMA_TriggerProcessor" -ScriptPath "C:\Scripts\TriggerFileProcessor.ps1"
#>

param(
    [Parameter(Mandatory=$true)]
    [string]$TaskName,
    
    [Parameter(Mandatory=$true)]
    [string]$ScriptPath,
    
    [Parameter(Mandatory=$false)]
    [string]$RunAsUser = $env:USERNAME,
    
    [Parameter(Mandatory=$false)]
    [int]$IntervalMinutes = 5,
    
    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\Logs\TriggerFileProcessor",
    
    [Parameter(Mandatory=$false)]
    [string]$TriggerFile = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\PROCESS_TRIGGER.txt"
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Output "[$Timestamp] [$Level] $Message"
}

try {
    Write-Log "Setting up scheduled task: $TaskName"
    
    # Verify script exists
    if (-not (Test-Path $ScriptPath)) {
        throw "Script not found: $ScriptPath"
    }
    
    # Create log directory if it doesn't exist
    if (-not (Test-Path $LogPath)) {
        New-Item -Path $LogPath -ItemType Directory -Force | Out-Null
        Write-Log "Created log directory: $LogPath"
    }
    
    # Remove existing task if it exists
    $ExistingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
    if ($ExistingTask) {
        Write-Log "Removing existing task: $TaskName"
        Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
    }
    
    # Build PowerShell command
    $PowerShellArgs = @(
        "-ExecutionPolicy Bypass"
        "-NoProfile"
        "-WindowStyle Hidden"
        "-File `"$ScriptPath`""
        "-TriggerFile `"$TriggerFile`""
        "-LogPath `"$LogPath`""
    )
    
    $PowerShellCommand = $PowerShellArgs -join " "
    
    # Create scheduled task action
    $Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument $PowerShellCommand
    
    # Create trigger to run every X minutes
    $Trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes $IntervalMinutes) -RepetitionDuration (New-TimeSpan -Days 365)
    
    # Create task settings
    $Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RunOnlyIfNetworkAvailable
    
    # Get credentials for the user account
    if ($RunAsUser -eq $env:USERNAME) {
        Write-Log "Task will run as current user: $RunAsUser"
        $Principal = New-ScheduledTaskPrincipal -UserId $RunAsUser -LogonType Interactive
    } else {
        Write-Log "Task will run as: $RunAsUser"
        $Credential = Get-Credential -UserName $RunAsUser -Message "Enter password for $RunAsUser"
        $Principal = New-ScheduledTaskPrincipal -UserId $RunAsUser -LogonType Password
    }
    
    # Register the scheduled task
    if ($RunAsUser -eq $env:USERNAME) {
        Register-ScheduledTask -TaskName $TaskName -Action $Action -Trigger $Trigger -Settings $Settings -Principal $Principal
    } else {
        Register-ScheduledTask -TaskName $TaskName -Action $Action -Trigger $Trigger -Settings $Settings -User $RunAsUser -Password $Credential.GetNetworkCredential().Password
    }
    
    Write-Log "Scheduled task created successfully: $TaskName"
    Write-Log "Task will run every $IntervalMinutes minutes"
    Write-Log "Script: $ScriptPath"
    Write-Log "Trigger File: $TriggerFile"
    Write-Log "Log Path: $LogPath"
    
    # Test the task
    Write-Log "Testing the scheduled task..."
    Start-ScheduledTask -TaskName $TaskName
    
    Start-Sleep -Seconds 5
    
    $TaskInfo = Get-ScheduledTaskInfo -TaskName $TaskName
    Write-Log "Task last run time: $($TaskInfo.LastRunTime)"
    Write-Log "Task last result: $($TaskInfo.LastTaskResult)"
    
    if ($TaskInfo.LastTaskResult -eq 0) {
        Write-Log "Task test completed successfully" "INFO"
    } else {
        Write-Log "Task test failed with result: $($TaskInfo.LastTaskResult)" "WARN"
    }
    
} catch {
    Write-Log "Error setting up scheduled task: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Log "Setup completed successfully"
