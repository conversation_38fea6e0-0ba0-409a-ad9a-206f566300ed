#yes/no load prophet results into postgres db
resultsLoadingFlag = 'yes'

#yes/no load csv files into postgres db
csvLoadingFlag = 'no'

#yes/no extraction to be performed
extractionFlag ='yes'

#results filters: required if resultsLoadingFlag set to yes
fileNames = [

]
runNumbers = [
  1
]
products = [
  "C_TERM"
]

resultsTypes = [
  "prj","imp"
]
spCodes = [
0,1
]
simulations = [

]
aggregationIdentifiers = ''
aggregationNames = ''
aggregationVariables = ''
policyIdentifiers = ''

monthFirst = 1
monthTimes = 12
yearFirst = None
yearTimes = None

#yes/no update structured tables when request completed
update_t_AggPrjData = 'no'
update_t_IMPData = 'yes'
update_t_PRJData = 'yes'
update_t_StoMData = 'no'
update_t_StoYData = 'no'

#csv loading settings: required if csvLoadingFlag set to yes
CsvFileNames = [
"Ref*.csv"
]
ListSeperator = ','
NumberSeperator = '.'
CurrentCulture = None
Quote = '"'

#extraction settings: required if extractionFlag set to yes
extractionSources = '"Analytics".vBI_PRJData'
#extraction filters: if there are no specified filter conditions, then data with current loading resultsLocation and requestDescription will be extracted
extractionFilters = 'requestdescription = \'EMO_RP[RP]_Step3\' and spcode=\'0\''
#extract file name
extractFileName = 'EMO_[RP]'

