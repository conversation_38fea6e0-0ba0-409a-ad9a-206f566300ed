#Requires -Version 5.0

<#
.SYNOPSIS
    Test script for ScheduledStaging.ps1 functionality
    
.DESCRIPTION
    This script tests the ScheduledStaging.ps1 trigger file logic by:
    1. Creating a test trigger file
    2. Running ScheduledStaging.ps1
    3. Verifying the trigger file was moved to TriggerProcessed
    
.PARAMETER ConfigFile
    Path to the staging configuration file
    
.PARAMETER TestSourceDir
    Test source directory (will be created if it doesn't exist)
    
.EXAMPLE
    .\Test-ScheduledStaging.ps1
#>

param(
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = ".\StagingConfig.json",
    
    [Parameter(Mandatory=$false)]
    [string]$TestSourceDir = "C:\Temp\TestStaging\Source"
)

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Output "[$Timestamp] [TEST-$Level] $Message"
}

try {
    Write-TestLog "=== Starting ScheduledStaging.ps1 Test ==="
    
    # Create test directories
    $TestDirs = @(
        $TestSourceDir,
        "$TestSourceDir\TriggerProcessed",
        "C:\Temp\TestStaging\Destination",
        "C:\Temp\TestStaging\Archive"
    )
    
    foreach ($Dir in $TestDirs) {
        if (-not (Test-Path $Dir)) {
            New-Item -Path $Dir -ItemType Directory -Force | Out-Null
            Write-TestLog "Created test directory: $Dir"
        }
    }
    
    # Create test configuration
    $TestConfig = @{
        SourceDirectory = $TestSourceDir
        DestinationDirectory = "C:\Temp\TestStaging\Destination"
        ArchiveDirectory = "C:\Temp\TestStaging\Archive"
        TriggerProcessedDirectory = "TriggerProcessed"
        FilePatterns = @("*.txt", "*.csv")
        RequireFileForSuccess = $false
        MaxRetries = 3
        RetryDelaySeconds = 30
    }
    
    $TestConfigFile = "C:\Temp\TestStaging\TestConfig.json"
    $TestConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $TestConfigFile -Encoding UTF8
    Write-TestLog "Created test configuration: $TestConfigFile"
    
    # Create some test files
    $TestFiles = @("test1.txt", "test2.csv", "test3.txt")
    foreach ($File in $TestFiles) {
        $FilePath = Join-Path $TestSourceDir $File
        "Test content for $File`nCreated: $(Get-Date)" | Out-File -FilePath $FilePath -Encoding UTF8
        Write-TestLog "Created test file: $File"
    }
    
    # Test 1: Run without trigger file (should exit without processing)
    Write-TestLog "=== Test 1: No Trigger File ==="
    $Result1 = & ".\ScheduledStaging.ps1" -ConfigFile $TestConfigFile -TestMode
    Write-TestLog "Test 1 Result: $LASTEXITCODE"
    
    if ($LASTEXITCODE -eq 0) {
        Write-TestLog "Test 1 PASSED: Script exited cleanly without trigger file" "SUCCESS"
    } else {
        Write-TestLog "Test 1 FAILED: Script should have exited with code 0" "ERROR"
    }
    
    # Test 2: Create trigger file and run (should process files)
    Write-TestLog "=== Test 2: With Trigger File ==="
    
    $TriggerFilePath = Join-Path $TestSourceDir "STAGING_TRIGGER.txt"
    $TriggerContent = @"
Test Trigger File
Created: $(Get-Date)
Purpose: Test ScheduledStaging.ps1 functionality

This trigger file should be moved to TriggerProcessed subdirectory.
"@
    
    $TriggerContent | Out-File -FilePath $TriggerFilePath -Encoding UTF8
    Write-TestLog "Created trigger file: $TriggerFilePath"
    
    # Verify trigger file exists
    if (Test-Path $TriggerFilePath) {
        Write-TestLog "Trigger file confirmed to exist"
    } else {
        throw "Failed to create trigger file"
    }
    
    # Run ScheduledStaging.ps1 with trigger file
    $Result2 = & ".\ScheduledStaging.ps1" -ConfigFile $TestConfigFile -TestMode
    Write-TestLog "Test 2 Result: $LASTEXITCODE"
    
    if ($LASTEXITCODE -eq 0) {
        Write-TestLog "Test 2 PASSED: Script completed successfully with trigger file" "SUCCESS"
    } else {
        Write-TestLog "Test 2 FAILED: Script should have completed successfully" "ERROR"
    }
    
    # Verify trigger file was moved
    if (-not (Test-Path $TriggerFilePath)) {
        Write-TestLog "Trigger file was successfully moved from source" "SUCCESS"
        
        # Check if it's in TriggerProcessed directory
        $ProcessedDir = Join-Path $TestSourceDir "TriggerProcessed"
        $ProcessedFiles = Get-ChildItem -Path $ProcessedDir -Filter "*STAGING_TRIGGER.txt" -ErrorAction SilentlyContinue
        
        if ($ProcessedFiles.Count -gt 0) {
            Write-TestLog "Trigger file found in TriggerProcessed directory: $($ProcessedFiles[0].Name)" "SUCCESS"
        } else {
            Write-TestLog "Trigger file NOT found in TriggerProcessed directory" "ERROR"
        }
    } else {
        Write-TestLog "Trigger file was NOT moved from source directory" "ERROR"
    }
    
    # Test 3: Run again without trigger file (should exit without processing)
    Write-TestLog "=== Test 3: No Trigger File (After Processing) ==="
    $Result3 = & ".\ScheduledStaging.ps1" -ConfigFile $TestConfigFile -TestMode
    Write-TestLog "Test 3 Result: $LASTEXITCODE"
    
    if ($LASTEXITCODE -eq 0) {
        Write-TestLog "Test 3 PASSED: Script exited cleanly without trigger file" "SUCCESS"
    } else {
        Write-TestLog "Test 3 FAILED: Script should have exited with code 0" "ERROR"
    }
    
    Write-TestLog "=== Test Summary ==="
    Write-TestLog "Test 1 (No Trigger): $(if ($Result1 -eq 0) { 'PASSED' } else { 'FAILED' })"
    Write-TestLog "Test 2 (With Trigger): $(if ($Result2 -eq 0) { 'PASSED' } else { 'FAILED' })"
    Write-TestLog "Test 3 (No Trigger Again): $(if ($Result3 -eq 0) { 'PASSED' } else { 'FAILED' })"
    
    Write-TestLog "=== ScheduledStaging.ps1 Test Completed ==="
    
    # Cleanup option
    $Cleanup = Read-Host "Do you want to clean up test files? (y/n)"
    if ($Cleanup -eq 'y' -or $Cleanup -eq 'Y') {
        Remove-Item -Path "C:\Temp\TestStaging" -Recurse -Force -ErrorAction SilentlyContinue
        Write-TestLog "Test files cleaned up"
    } else {
        Write-TestLog "Test files preserved at: C:\Temp\TestStaging"
    }
    
} catch {
    Write-TestLog "Test failed with error: $($_.Exception.Message)" "ERROR"
    exit 1
}
