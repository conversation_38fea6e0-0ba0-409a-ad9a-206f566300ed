import requests
import json
from datetime import date, datetime
from colorama import Fore
import time
import pandas as pd
import snowflake.connector
import os
import sys
import importlib
import re
import csv
from sqlalchemy import create_engine,text
import urllib.parse
from snowflake.connector.pandas_tools import write_pandas
from cryptography.fernet import Fernet
import DirectModeConfig as config
import DirectModeModules as fn

workspace = os.path.dirname(os.path.abspath(__file__)) 
logFile = os.path.join(workspace, 'LogFiles', 'DirectModeRunScript' + date.today().strftime('%Y%m%d') + '.txt')
SourcesFolder = os.path.join(workspace, 'Sources')
ReportsFolder = os.path.join(workspace, 'Reports')

# If using encrypted password
if config.SnowflakePassword_encrypted:
    snowflakePassword = Fernet(config.SnowflakePassword_encryptKey).decrypt(config.SnowflakePassword_encrypted).decode("utf-8")
else:
    snowflakePassword = config.SnowflakePassword

# URL encode the password to handle special characters
encoded_password = urllib.parse.quote_plus(snowflakePassword)

# Create connection strings
snowflakeControlDbConnectionString = (
    f"snowflake://{config.SnowflakeUsername}:{encoded_password}@"
    f"{config.SnowflakeAccount}/{config.SnowflakeControlDatabase}/"
    f"{config.SnowflakeSchema}?"
    f"warehouse={config.SnowflakeWarehouse}&"
    f"role={config.SnowflakeRole}"
)

snowflakeResultsDbConnectionString = (
    f"snowflake://{config.SnowflakeUsername}:{encoded_password}@"
    f"{config.SnowflakeAccount}/{config.SnowflakeResultsDatabase}/"
    f"{config.SnowflakeSchema}?"
    f"warehouse={config.SnowflakeWarehouse}&"
    f"role={config.SnowflakeRole}"
)

try:
    
    # check if env variable PROPHET_RESULTS_DIR exists
    prophet_results_dir = os.getenv('PROPHET_RESULTS_DIR')
    if prophet_results_dir is not None:
        ResultsLocation = prophet_results_dir
        job_dir = os.getenv('JOB_DIR')
        workspace_dir = os.getenv('WORKSPACE_DIR')
        requestDescription = job_dir.split('\\')[-1]
        fn.writeLog (logFile,'Info','job_dir: ' + job_dir)
        fn.writeLog (logFile,'Info','workspace_dir: ' + workspace_dir)
    else:
        fn.writeLog (logFile,'Info','PROPHET_RESULTS_DIR not available')
        ResultsLocation = input('please enter propet results location: ')
        requestDescription = input('please enter request description (optional): ')
        if requestDescription is None:
            requestDescription = 'Manual_load_'+ ResultsLocation.split('\\')[-2]
        
    
    #reading results settings
    fn.writeLog (logFile,'Step Info','reading results settings')
    fn.writeLog (logFile,'Info','ResultsLocation: ' + ResultsLocation)
    fn.writeLog (logFile,'Info','requestDescription: ' + requestDescription)
    
    
    #set default values
    update_t_AggPrjData = 'no'
    update_t_IMPData = 'no'
    update_t_PRJData = 'no'
    update_t_StoMData = 'no'
    update_t_StoYData = 'no'
    requestFilters = ''
    CsvRequestFilters = ''
    CsvParameters = ''
    extractionSources = ''
    extractionFilters = 'requestDescription = [requestDescription] and resultslocation = [resultslocation]'
    varListValues = ''
    variable_filter = f"''"  
    requestUrl = "http://" + config.IDRWebServer + ":100/api/v1/requests"
    
    #Reading Step nbr from requestDescription
    matchStep = re.search(r'Step\d+',requestDescription)
    if matchStep is not None:
        Step = matchStep.group()
    else:
        Step = ''
        
    #Reading reportingCycle from requestDescription
    matchRC = re.search(r'RP(.{6})',requestDescription)
    if matchRC is not None:
        reportingCycle = matchRC.group(1)
    else:
        reportingCycle = ''
    
    #Check if there's Step Config file
    StepConfigFile = os.path.join(workspace, 'StepConfig', Step+'.py')
    if os.path.isfile(StepConfigFile):
        fn.writeLog (logFile,'Info','Reading Step Config file: ' + StepConfigFile)
        step_settings = importlib.import_module('StepConfig.'+Step)
        resultsLoadingFlag = step_settings.resultsLoadingFlag
        csvLoadingFlag = step_settings.csvLoadingFlag
        extractionFlag = step_settings.extractionFlag
        if resultsLoadingFlag == 'yes':
            update_t_AggPrjData = step_settings.update_t_AggPrjData
            update_t_IMPData = step_settings.update_t_IMPData
            update_t_PRJData = step_settings.update_t_PRJData
            update_t_StoMData = step_settings.update_t_StoMData
            update_t_StoYData = step_settings.update_t_StoYData
            requestFilters = fn.generateRequestFilters(step_settings.fileNames,step_settings.runNumbers,step_settings.products,step_settings.resultsTypes,step_settings.spCodes,step_settings.simulations,step_settings.aggregationIdentifiers,step_settings.aggregationNames,step_settings.aggregationVariables,step_settings.policyIdentifiers,step_settings.monthFirst,step_settings.monthTimes,step_settings.yearFirst,step_settings.yearTimes)
        if csvLoadingFlag == 'yes':
            CsvRequestFilters = fn.generateCsvRequestFilters(step_settings.CsvFileNames)
            CsvParameters = fn.generateCsvParameters(step_settings.ListSeperator,step_settings.NumberSeperator,step_settings.CurrentCulture,step_settings.Quote)
        if extractionFlag == 'yes':
            extractionSources = step_settings.extractionSources
            if extractionSources == '':
                raise ValueError("extractionSource not valid")
            
            if step_settings.extractionFilters != '':
                extractionFilters = step_settings.extractionFilters.replace('[RP]',reportingCycle)

            extractFileName = step_settings.extractFileName
            
    else :
        fn.writeLog (logFile,'Info','No Step config')
        resultsLoadingFlag = 'yes'
        csvLoadingFlag = 'no'
        extractionFlag = 'no'
        requestFilters = fn.generateRequestFilters([],[],[],[],[],[],'','','','',None,None,None,None)
        
        
    #Check if there's variable list file
    VarFile = os.path.join(workspace, 'StepConfig', Step+'.var')
    if os.path.isfile(VarFile):
        with open(VarFile) as varListFile:
            varList = varListFile.read().splitlines()
        varListValues = "'', ''".join(varList)
        variable_filter = f"'AND r2_1.value::text in (''{varListValues}'')'"

    
    fn.writeLog (logFile,'Info','resultsLoadingFlag: ' + resultsLoadingFlag)
    fn.writeLog (logFile,'Info','csvLoadingFlag: ' + csvLoadingFlag)
    fn.writeLog (logFile,'Info','extractionFlag: ' + extractionFlag)
    
    
    if resultsLoadingFlag == 'yes':
        fn.writeLog (logFile,'Info','update_t_AggPrjData: ' + update_t_AggPrjData)
        fn.writeLog (logFile,'Info','update_t_IMPData: ' + update_t_IMPData)
        fn.writeLog (logFile,'Info','update_t_PRJData: ' + update_t_PRJData)
        fn.writeLog (logFile,'Info','update_t_StoMData: ' + update_t_StoMData)
        fn.writeLog (logFile,'Info','update_t_StoYData: ' + update_t_StoYData)
    
        fn.writeLog (logFile,'Info','requestFilters: ' + requestFilters)
        fn.writeLog (logFile,'Info','variable_filter: ' + variable_filter)

        #create new load request
        fn.writeLog (logFile,'Step Info','Creating new load request')
        fn.writeLog (logFile,'Info','requestUrl: ' + requestUrl)
    
        response = fn.createRequest (requestDescription,ResultsLocation,requestFilters,logFile,requestUrl)
        fn.writeLog (logFile,'Info',str(response))
        if response.ok:
            fn.writeLog (logFile,'Info','Request was successful.')
        else:
            fn.writeLog (logFile,'Error',f"Error: {response.text}" )
            sys.exit(1)  

    if (resultsLoadingFlag == 'yes' and config.check_request_progress == 'yes'):
        #connect to snowflake control database
        fn.writeLog (logFile,'Step Info','connecting to Snowflake control database')        
        SnowflakeControlDbConnect = create_engine(snowflakeControlDbConnectionString)
        
        # check request status
        with SnowflakeControlDbConnect.connect() as snowflakeControlDbConnection:
            fn.writeLog (logFile,'Info','connected to ' + config.SnowflakeControlDatabase)
            time.sleep(5) 
            checkRequestStatusQuery = 'SELECT requestid, requestdescription, statusid FROM ' + config.IDRControlDbSchema + '.request ORDER BY 1 DESC LIMIT 1'
            fn.writeLog (logFile,'Info','checkRequestStatusQuery: ' + str(checkRequestStatusQuery))
            results = snowflakeControlDbConnection.execute(text(checkRequestStatusQuery)).fetchone()
            requestid, requestdescription, statusid = results
            fn.writeLog (logFile,'Info','requestid: ' + str(requestid))
            fn.writeLog (logFile,'Info','requestdescription: ' + requestdescription)
            
        check_request_iteration = 0
        while check_request_iteration < config.max_check_iteration:
            with SnowflakeControlDbConnect.connect() as snowflakeControlDbConnection:
                results = snowflakeControlDbConnection.execute(text(checkRequestStatusQuery)).fetchone()
            requestid, requestdescription, statusid = results
            fn.writeLog (logFile,'Info','request (' + str(requestid) + ') in progress (status ' + str(statusid) + ')')
            if statusid not in [3,4,11]:
                break
            check_request_iteration += 1
            fn.writeLog (logFile,'Info','wait ' + str(config.check_request_wait_time) + 's for next status check')
            time.sleep(config.check_request_wait_time)
        
        # request failed
        if statusid == 6:
            fn.writeLog (logFile,'Error','request failed, please check db log for more information')
            raise SystemExit()
        
        # request cancelled
        if statusid == 7:
            fn.writeLog (logFile,'Error','request cancelled')
            raise SystemExit()    
        
        # request completed
        if statusid == 5:
            fn.writeLog (logFile,'Success','request completed')
            
            # update structured tables
            if (update_t_AggPrjData == 'yes' or update_t_IMPData == 'yes' or update_t_PRJData == 'yes' or update_t_StoMData == 'yes' or update_t_StoYData == 'yes'):
                fn.writeLog (logFile,'Step Info','connecting to snowflake results database')
                snowflakeResultsDbConnect = create_engine(snowflakeResultsDbConnectionString)
                raw_connection = snowflakeResultsDbConnect.raw_connection()
                cursor = raw_connection.cursor()
                fn.writeLog (logFile,'Info','connected to ' + config.snowflakeResultsDatabase)
            
                if (update_t_AggPrjData == 'yes'):
                    fn.writeLog (logFile,'Info','updating t_AggPrjData')
                    fn.writeLog (logFile,'Info','CALL "Analytics".spLoad_t_AggPRJData('+str(requestid) + ',' + variable_filter + '::text)')
                    cursor.execute('CALL "Analytics".spLoad_t_AggPRJData('+str(requestid) + ',' + variable_filter + '::text)')

                if (update_t_IMPData == 'yes'):
                    fn.writeLog (logFile,'Info','updating t_IMPData')
                    fn.writeLog (logFile,'Info','CALL "Analytics".spLoad_t_IMPData('+str(requestid) + ',' + variable_filter + '::text)')
                    cursor.execute('CALL "Analytics".spLoad_t_IMPData('+str(requestid) + ',' + variable_filter + '::text)')

                if (update_t_PRJData == 'yes'):
                    fn.writeLog (logFile,'Info','updating t_PRJData')
                    fn.writeLog (logFile,'Info','CALL "Analytics".spLoad_t_PRJData('+str(requestid) + ',' + variable_filter + '::text)')
                    cursor.execute('CALL "Analytics".spLoad_t_PRJData('+str(requestid) + ',' + variable_filter + '::text)')

                if (update_t_StoMData == 'yes'):
                    fn.writeLog (logFile,'Info','updating t_StoMData')
                    fn.writeLog (logFile,'Info','CALL "Analytics".spLoad_t_StoMData('+str(requestid) + ',' + variable_filter + '::text)')
                    cursor.execute('CALL "Analytics".spLoad_t_StoMData('+str(requestid) + ',' + variable_filter + '::text)')

                if (update_t_StoYData == 'yes'):
                    fn.writeLog (logFile,'Info','updating t_StoYData')
                    fn.writeLog (logFile,'Info','CALL "Analytics".spLoad_t_StoYData('+str(requestid) + ',' + variable_filter + '::text)')
                    cursor.execute('CALL "Analytics".spLoad_t_StoYData('+str(requestid) + ',' + variable_filter + '::text)')
                      
                for notice in raw_connection.notices:
                    fn.writeLog (logFile,'Info',notice)
                    
                raw_connection.commit()
                cursor.close()
                raw_connection.close()
    
    # start csv load -- TBD: IDR 2023Q2SP6 version has bug when change source type
    if csvLoadingFlag == 'yes':
        fn.writeLog (logFile,'Info','CsvRequestFilters: ' + CsvRequestFilters)
        fn.writeLog (logFile,'Info','CsvParameters: ' + CsvParameters)
        
        #create new csv load request
        fn.writeLog (logFile,'Step Info','Creating new csv load request')
        requestUrl = "http://" + config.IDRWebServer + ":100/api/v1/requests"
        fn.writeLog (logFile,'Info','requestUrl: ' + requestUrl)
    
        response = fn.createCsvRequest (requestDescription,SourcesFolder,CsvRequestFilters,CsvParameters,logFile,requestUrl)
        fn.writeLog (logFile,'Info',str(response))
    
    # start extraction
    if extractionFlag == 'yes':
        fn.writeLog (logFile,'Step Info','Generating extraction')
        
        if extractFileName == '':
            raise ValueError("extractFileName not valid")
        else:
            report = ReportsFolder + '\\' + extractFileName.replace('[RP]',reportingCycle) + '.csv'
            
        fn.writeLog (logFile,'Info','extracting ' + report)
        
        extract_query = 'select * from ' + extractionSources + ' where ' + extractionFilters.replace('[requestDescription]', '\'' + requestDescription + '\'')
        fn.writeLog (logFile,'Info','extract_query: ' + extract_query)
        
        snowflakeResultsDbConnect = create_engine(snowflakeResultsDbConnectionString)
        raw_connection = snowflakeResultsDbConnect.raw_connection()
        cursor = raw_connection.cursor()
        cursor.execute(extract_query)
        
        #output of extract_query
        rows = cursor.fetchall()
        
        #get headers
        column_names = [desc[0] for desc in cursor.description]
        
        with open(report, mode='w', newline='') as file:
            writer = csv.writer(file)
            writer.writerow(column_names)
            writer.writerows(rows)
        cursor.close()
        raw_connection.close()
        
        fn.writeLog (logFile,'Success','extraction completed')
    
except Exception as ex:
    fn.writeLog(logFile,'Error','Error: ' + str(ex))
    raise SystemExit()