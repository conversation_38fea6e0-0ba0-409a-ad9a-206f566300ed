#Requires -Version 5.0

<#
.SYNOPSIS
    Monitors for trigger files and processes SFTP files when trigger is found
    
.DESCRIPTION
    This script is designed to run periodically via Task Scheduler.
    It looks for a trigger file and when found, processes files from SFTP to destination directories.
    Based on the XML step: PullSFTPFiles
    
.PARAMETER TriggerFile
    Path to the trigger file to monitor
    
.PARAMETER LogPath
    Path to write log files
    
.PARAMETER MaxLogDays
    Number of days to keep log files (default: 30)
    
.EXAMPLE
    .\TriggerFileProcessor.ps1 -TriggerFile "\\server\share\trigger.txt" -LogPath "C:\Logs"
#>

param(
    [Parameter(Mandatory=$false)]
    [string]$TriggerFile = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\PROCESS_TRIGGER.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\Logs\TriggerFileProcessor",
    
    [Parameter(Mandatory=$false)]
    [int]$MaxLogDays = 30,
    
    [Parameter(Mandatory=$false)]
    [switch]$TestMode
)

# Configuration based on your XML step
$Config = @{
    SourceDirectory = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\"
    DestinationDirectory = "\\MDTMNASPAWFS001\PPData$\2_TEST\InputFiles\Entities\TMA\Load\"
    ArchiveDirectory = "\\MDTMNASPAWFS001\PPData$\2_TEST\InputFiles\Entities\TMA\Processed\"
    ProcessedTriggerDirectory = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\ProcessedTriggers\"
    RequireFileForSuccess = $true
    FilePatterns = @("*.csv", "*.xml", "*.txt")  # Add your file patterns
}

# Initialize logging
if (-not (Test-Path $LogPath)) {
    New-Item -Path $LogPath -ItemType Directory -Force | Out-Null
}

$LogFile = Join-Path $LogPath "TriggerProcessor_$(Get-Date -Format 'yyyyMMdd').log"

function Write-Log {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "DEBUG")]
        [string]$Level = "INFO"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    
    # Write to console and log file
    Write-Output $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry -Encoding UTF8
}

function Test-NetworkPath {
    param([string]$Path)
    
    try {
        if (Test-Path $Path -ErrorAction Stop) {
            return $true
        }
    } catch {
        Write-Log "Cannot access path: $Path - $($_.Exception.Message)" "ERROR"
        return $false
    }
    return $false
}

function Move-FilesWithArchive {
    param(
        [string]$SourceDir,
        [string]$DestDir,
        [string]$ArchiveDir,
        [string[]]$FilePatterns
    )
    
    $FilesProcessed = 0
    $Errors = @()
    
    try {
        # Ensure destination and archive directories exist
        if (-not (Test-Path $DestDir)) {
            New-Item -Path $DestDir -ItemType Directory -Force | Out-Null
            Write-Log "Created destination directory: $DestDir"
        }
        
        if (-not (Test-Path $ArchiveDir)) {
            New-Item -Path $ArchiveDir -ItemType Directory -Force | Out-Null
            Write-Log "Created archive directory: $ArchiveDir"
        }
        
        # Process each file pattern
        foreach ($Pattern in $FilePatterns) {
            $SourcePattern = Join-Path $SourceDir $Pattern
            $Files = Get-ChildItem -Path $SourcePattern -File -ErrorAction SilentlyContinue
            
            foreach ($File in $Files) {
                try {
                    $DestPath = Join-Path $DestDir $File.Name
                    $ArchivePath = Join-Path $ArchiveDir $File.Name
                    
                    # Copy to destination
                    Copy-Item -Path $File.FullName -Destination $DestPath -Force
                    Write-Log "Copied: $($File.Name) -> Destination"
                    
                    # Copy to archive
                    Copy-Item -Path $File.FullName -Destination $ArchivePath -Force
                    Write-Log "Copied: $($File.Name) -> Archive"
                    
                    # Remove from source (only after successful copies)
                    Remove-Item -Path $File.FullName -Force
                    Write-Log "Removed from source: $($File.Name)"
                    
                    $FilesProcessed++
                    
                } catch {
                    $ErrorMsg = "Failed to process file $($File.Name): $($_.Exception.Message)"
                    Write-Log $ErrorMsg "ERROR"
                    $Errors += $ErrorMsg
                }
            }
        }
        
    } catch {
        $ErrorMsg = "Error in file processing: $($_.Exception.Message)"
        Write-Log $ErrorMsg "ERROR"
        $Errors += $ErrorMsg
    }
    
    return @{
        FilesProcessed = $FilesProcessed
        Errors = $Errors
    }
}

function Remove-OldLogs {
    param([string]$LogDirectory, [int]$DaysToKeep)
    
    try {
        $CutoffDate = (Get-Date).AddDays(-$DaysToKeep)
        $OldLogs = Get-ChildItem -Path $LogDirectory -Filter "*.log" | Where-Object { $_.LastWriteTime -lt $CutoffDate }
        
        foreach ($Log in $OldLogs) {
            Remove-Item -Path $Log.FullName -Force
            Write-Log "Removed old log: $($Log.Name)" "DEBUG"
        }
    } catch {
        Write-Log "Error cleaning old logs: $($_.Exception.Message)" "WARN"
    }
}

function Process-TriggerFile {
    param([string]$TriggerFilePath)
    
    try {
        # Read trigger file content for any special instructions
        $TriggerContent = Get-Content -Path $TriggerFilePath -Raw -ErrorAction Stop
        Write-Log "Trigger file content: $TriggerContent"
        
        # Move trigger file to processed directory
        $ProcessedTriggerPath = Join-Path $Config.ProcessedTriggerDirectory "$(Get-Date -Format 'yyyyMMdd_HHmmss')_$(Split-Path $TriggerFilePath -Leaf)"
        
        if (-not (Test-Path $Config.ProcessedTriggerDirectory)) {
            New-Item -Path $Config.ProcessedTriggerDirectory -ItemType Directory -Force | Out-Null
        }
        
        Move-Item -Path $TriggerFilePath -Destination $ProcessedTriggerPath -Force
        Write-Log "Moved trigger file to: $ProcessedTriggerPath"
        
        return $true
        
    } catch {
        Write-Log "Error processing trigger file: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main execution
try {
    Write-Log "=== Starting Trigger File Processor ===" "INFO"
    Write-Log "Trigger File: $TriggerFile"
    Write-Log "Source Directory: $($Config.SourceDirectory)"
    Write-Log "Destination Directory: $($Config.DestinationDirectory)"
    Write-Log "Archive Directory: $($Config.ArchiveDirectory)"
    
    if ($TestMode) {
        Write-Log "Running in TEST MODE" "WARN"
    }
    
    # Clean old logs
    Remove-OldLogs -LogDirectory $LogPath -DaysToKeep $MaxLogDays
    
    # Check if trigger file exists
    if (-not (Test-Path $TriggerFile)) {
        Write-Log "No trigger file found: $TriggerFile" "DEBUG"
        exit 0
    }
    
    Write-Log "Trigger file found: $TriggerFile" "INFO"
    
    # Validate network paths
    $PathsToCheck = @(
        $Config.SourceDirectory,
        $Config.DestinationDirectory,
        $Config.ArchiveDirectory
    )
    
    foreach ($Path in $PathsToCheck) {
        if (-not (Test-NetworkPath $Path)) {
            Write-Log "Cannot access required path: $Path" "ERROR"
            exit 1
        }
    }
    
    # Process the trigger file
    if (-not (Process-TriggerFile $TriggerFile)) {
        Write-Log "Failed to process trigger file" "ERROR"
        exit 1
    }
    
    # Move files from source to destination and archive
    Write-Log "Starting file move operation..."
    
    if ($TestMode) {
        Write-Log "TEST MODE: Would process files matching patterns: $($Config.FilePatterns -join ', ')"
        $TestFiles = @()
        foreach ($Pattern in $Config.FilePatterns) {
            $TestFiles += Get-ChildItem -Path (Join-Path $Config.SourceDirectory $Pattern) -File -ErrorAction SilentlyContinue
        }
        Write-Log "TEST MODE: Found $($TestFiles.Count) files to process"
        foreach ($File in $TestFiles) {
            Write-Log "TEST MODE: Would process: $($File.Name)"
        }
    } else {
        $Result = Move-FilesWithArchive -SourceDir $Config.SourceDirectory -DestDir $Config.DestinationDirectory -ArchiveDir $Config.ArchiveDirectory -FilePatterns $Config.FilePatterns
        
        Write-Log "File processing completed. Files processed: $($Result.FilesProcessed)"
        
        if ($Result.Errors.Count -gt 0) {
            Write-Log "Errors encountered during processing:" "WARN"
            foreach ($Error in $Result.Errors) {
                Write-Log $Error "ERROR"
            }
        }
        
        # Check if files were required for success
        if ($Config.RequireFileForSuccess -and $Result.FilesProcessed -eq 0) {
            Write-Log "No files were processed but files are required for success" "ERROR"
            exit 1
        }
    }
    
    Write-Log "=== Trigger File Processor Completed Successfully ===" "INFO"
    exit 0
    
} catch {
    Write-Log "Unexpected error in main execution: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}
