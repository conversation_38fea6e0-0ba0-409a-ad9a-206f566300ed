#Requires -Version 5.0

<#
.SYNOPSIS
    Monitors for trigger files and executes ScheduledStaging.ps1 when trigger is found
    
.DESCRIPTION
    This script is designed to run periodically via Task Scheduler.
    It looks for a trigger file and when found, executes the ScheduledStaging.ps1 script.
    The trigger file is processed (moved to archive) after successful execution.
    
.PARAMETER TriggerFile
    Path to the trigger file to monitor
    
.PARAMETER StagingScript
    Path to the ScheduledStaging.ps1 script to execute
    
.PARAMETER LogPath
    Path to write log files
    
.PARAMETER MaxLogDays
    Number of days to keep log files (default: 30)
    
.PARAMETER TimeoutMinutes
    Timeout for ScheduledStaging.ps1 execution in minutes (default: 60)
    
.EXAMPLE
    .\TriggerStagingMonitor.ps1 -TriggerFile "\\server\share\staging_trigger.txt" -StagingScript "C:\Scripts\ScheduledStaging.ps1"
#>

param(
    [Parameter(Mandatory=$false)]
    [string]$TriggerFile = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\STAGING_TRIGGER.txt",
    
    [Parameter(Mandatory=$false)]
    [string]$StagingScript = "C:\Scripts\ScheduledStaging.ps1",
    
    [Parameter(Mandatory=$false)]
    [string]$LogPath = "C:\Logs\TriggerStagingMonitor",
    
    [Parameter(Mandatory=$false)]
    [int]$MaxLogDays = 30,
    
    [Parameter(Mandatory=$false)]
    [int]$TimeoutMinutes = 60,
    
    [Parameter(Mandatory=$false)]
    [switch]$TestMode
)

# Configuration
$Config = @{
    ProcessedTriggerDirectory = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\ProcessedTriggers\"
    ErrorTriggerDirectory = "\\MDTMNASPAWFS001\SFTPData$\AutoIncomingTMNAS\Test\TMA\ErrorTriggers\"
    MaxRetries = 3
    RetryDelaySeconds = 30
}

# Initialize logging
if (-not (Test-Path $LogPath)) {
    New-Item -Path $LogPath -ItemType Directory -Force | Out-Null
}

$LogFile = Join-Path $LogPath "TriggerStagingMonitor_$(Get-Date -Format 'yyyyMMdd').log"

function Write-Log {
    param(
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "DEBUG", "SUCCESS")]
        [string]$Level = "INFO"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    
    # Write to console and log file
    Write-Output $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry -Encoding UTF8
    
    # Also write to event log for important events
    if ($Level -eq "ERROR") {
        try {
            Write-EventLog -LogName Application -Source "TriggerStagingMonitor" -EventId 1001 -EntryType Error -Message $Message -ErrorAction SilentlyContinue
        } catch {
            # Ignore if event source doesn't exist
        }
    }
}

function Test-NetworkPath {
    param([string]$Path)
    
    try {
        if (Test-Path $Path -ErrorAction Stop) {
            return $true
        }
    } catch {
        Write-Log "Cannot access path: $Path - $($_.Exception.Message)" "ERROR"
        return $false
    }
    return $false
}

function Remove-OldLogs {
    param([string]$LogDirectory, [int]$DaysToKeep)
    
    try {
        $CutoffDate = (Get-Date).AddDays(-$DaysToKeep)
        $OldLogs = Get-ChildItem -Path $LogDirectory -Filter "*.log" | Where-Object { $_.LastWriteTime -lt $CutoffDate }
        
        foreach ($Log in $OldLogs) {
            Remove-Item -Path $Log.FullName -Force
            Write-Log "Removed old log: $($Log.Name)" "DEBUG"
        }
    } catch {
        Write-Log "Error cleaning old logs: $($_.Exception.Message)" "WARN"
    }
}

function Process-TriggerFile {
    param([string]$TriggerFilePath)
    
    try {
        # Read trigger file content for any special instructions
        $TriggerContent = Get-Content -Path $TriggerFilePath -Raw -ErrorAction Stop
        Write-Log "Trigger file content: $TriggerContent"
        
        # Parse trigger file for any parameters (optional)
        $TriggerParams = @{}
        if ($TriggerContent -match "PARAMETERS:\s*(.+)") {
            try {
                $ParamString = $matches[1].Trim()
                $TriggerParams = ConvertFrom-StringData -StringData $ParamString
                Write-Log "Parsed trigger parameters: $($TriggerParams | ConvertTo-Json -Compress)"
            } catch {
                Write-Log "Could not parse trigger parameters, using defaults" "WARN"
            }
        }
        
        # Ensure processed trigger directory exists
        if (-not (Test-Path $Config.ProcessedTriggerDirectory)) {
            New-Item -Path $Config.ProcessedTriggerDirectory -ItemType Directory -Force | Out-Null
        }
        
        # Move trigger file to processed directory with timestamp
        $ProcessedTriggerPath = Join-Path $Config.ProcessedTriggerDirectory "$(Get-Date -Format 'yyyyMMdd_HHmmss')_$(Split-Path $TriggerFilePath -Leaf)"
        Move-Item -Path $TriggerFilePath -Destination $ProcessedTriggerPath -Force
        Write-Log "Moved trigger file to: $ProcessedTriggerPath"
        
        return @{
            Success = $true
            Parameters = $TriggerParams
            ProcessedPath = $ProcessedTriggerPath
        }
        
    } catch {
        Write-Log "Error processing trigger file: $($_.Exception.Message)" "ERROR"
        
        # Move to error directory if possible
        try {
            if (-not (Test-Path $Config.ErrorTriggerDirectory)) {
                New-Item -Path $Config.ErrorTriggerDirectory -ItemType Directory -Force | Out-Null
            }
            $ErrorTriggerPath = Join-Path $Config.ErrorTriggerDirectory "$(Get-Date -Format 'yyyyMMdd_HHmmss')_ERROR_$(Split-Path $TriggerFilePath -Leaf)"
            Move-Item -Path $TriggerFilePath -Destination $ErrorTriggerPath -Force -ErrorAction SilentlyContinue
            Write-Log "Moved failed trigger file to: $ErrorTriggerPath" "WARN"
        } catch {
            Write-Log "Could not move failed trigger file to error directory" "ERROR"
        }
        
        return @{
            Success = $false
            Parameters = @{}
            ProcessedPath = $null
        }
    }
}

function Execute-StagingScript {
    param(
        [string]$ScriptPath,
        [hashtable]$Parameters = @{},
        [int]$TimeoutMinutes
    )
    
    try {
        Write-Log "Starting execution of ScheduledStaging.ps1"
        Write-Log "Script path: $ScriptPath"
        Write-Log "Timeout: $TimeoutMinutes minutes"
        
        if ($Parameters.Count -gt 0) {
            Write-Log "Additional parameters: $($Parameters | ConvertTo-Json -Compress)"
        }
        
        # Build PowerShell arguments
        $Arguments = @(
            "-ExecutionPolicy", "Bypass"
            "-NoProfile"
            "-File", "`"$ScriptPath`""
        )
        
        # Add any parameters from trigger file
        foreach ($Key in $Parameters.Keys) {
            $Arguments += "-$Key"
            $Arguments += "`"$($Parameters[$Key])`""
        }
        
        if ($TestMode) {
            Write-Log "TEST MODE: Would execute PowerShell with arguments: $($Arguments -join ' ')"
            return @{
                Success = $true
                ExitCode = 0
                Output = "TEST MODE - No actual execution"
                ExecutionTime = "00:00:00"
            }
        }
        
        # Start the process with timeout
        $StartTime = Get-Date
        $Process = Start-Process -FilePath "PowerShell.exe" -ArgumentList $Arguments -NoNewWindow -PassThru -RedirectStandardOutput "$LogPath\staging_output_$(Get-Date -Format 'yyyyMMdd_HHmmss').log" -RedirectStandardError "$LogPath\staging_error_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
        
        # Wait for completion with timeout
        $TimeoutMs = $TimeoutMinutes * 60 * 1000
        $Completed = $Process.WaitForExit($TimeoutMs)
        $EndTime = Get-Date
        $ExecutionTime = $EndTime - $StartTime
        
        if (-not $Completed) {
            # Process timed out
            Write-Log "ScheduledStaging.ps1 execution timed out after $TimeoutMinutes minutes" "ERROR"
            $Process.Kill()
            $Process.WaitForExit(5000)  # Wait up to 5 seconds for cleanup
            
            return @{
                Success = $false
                ExitCode = -1
                Output = "Process timed out"
                ExecutionTime = $ExecutionTime.ToString()
            }
        }
        
        $ExitCode = $Process.ExitCode
        Write-Log "ScheduledStaging.ps1 completed with exit code: $ExitCode"
        Write-Log "Execution time: $($ExecutionTime.ToString())"
        
        # Read output files
        $OutputFile = "$LogPath\staging_output_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
        $ErrorFile = "$LogPath\staging_error_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
        
        $Output = ""
        if (Test-Path $OutputFile) {
            $Output = Get-Content $OutputFile -Raw -ErrorAction SilentlyContinue
        }
        
        $ErrorOutput = ""
        if (Test-Path $ErrorFile) {
            $ErrorOutput = Get-Content $ErrorFile -Raw -ErrorAction SilentlyContinue
        }
        
        if ($ErrorOutput) {
            Write-Log "ScheduledStaging.ps1 error output: $ErrorOutput" "WARN"
        }
        
        return @{
            Success = ($ExitCode -eq 0)
            ExitCode = $ExitCode
            Output = $Output
            ErrorOutput = $ErrorOutput
            ExecutionTime = $ExecutionTime.ToString()
        }
        
    } catch {
        Write-Log "Error executing ScheduledStaging.ps1: $($_.Exception.Message)" "ERROR"
        return @{
            Success = $false
            ExitCode = -999
            Output = $_.Exception.Message
            ExecutionTime = "00:00:00"
        }
    }
}

# Main execution
try {
    Write-Log "=== Starting Trigger Staging Monitor ===" "INFO"
    Write-Log "Trigger File: $TriggerFile"
    Write-Log "Staging Script: $StagingScript"
    Write-Log "Log Path: $LogPath"
    Write-Log "Timeout: $TimeoutMinutes minutes"
    
    if ($TestMode) {
        Write-Log "Running in TEST MODE" "WARN"
    }
    
    # Clean old logs
    Remove-OldLogs -LogDirectory $LogPath -DaysToKeep $MaxLogDays
    
    # Check if trigger file exists
    if (-not (Test-Path $TriggerFile)) {
        Write-Log "No trigger file found: $TriggerFile" "DEBUG"
        exit 0
    }
    
    Write-Log "Trigger file found: $TriggerFile" "INFO"
    
    # Verify staging script exists
    if (-not (Test-Path $StagingScript)) {
        Write-Log "ScheduledStaging.ps1 script not found: $StagingScript" "ERROR"
        exit 1
    }
    
    # Validate required directories
    $RequiredPaths = @(
        $Config.ProcessedTriggerDirectory,
        $Config.ErrorTriggerDirectory
    )
    
    foreach ($Path in $RequiredPaths) {
        $ParentDir = Split-Path $Path -Parent
        if (-not (Test-NetworkPath $ParentDir)) {
            Write-Log "Cannot access required parent directory: $ParentDir" "ERROR"
            exit 1
        }
    }
    
    # Process the trigger file
    Write-Log "Processing trigger file..."
    $TriggerResult = Process-TriggerFile $TriggerFile
    
    if (-not $TriggerResult.Success) {
        Write-Log "Failed to process trigger file" "ERROR"
        exit 1
    }
    
    # Execute ScheduledStaging.ps1
    Write-Log "Executing ScheduledStaging.ps1..."
    $ExecutionResult = Execute-StagingScript -ScriptPath $StagingScript -Parameters $TriggerResult.Parameters -TimeoutMinutes $TimeoutMinutes
    
    if ($ExecutionResult.Success) {
        Write-Log "ScheduledStaging.ps1 executed successfully" "SUCCESS"
        Write-Log "Execution time: $($ExecutionResult.ExecutionTime)"
        if ($ExecutionResult.Output) {
            Write-Log "Script output: $($ExecutionResult.Output)" "DEBUG"
        }
    } else {
        Write-Log "ScheduledStaging.ps1 execution failed with exit code: $($ExecutionResult.ExitCode)" "ERROR"
        if ($ExecutionResult.Output) {
            Write-Log "Script output: $($ExecutionResult.Output)" "ERROR"
        }
        if ($ExecutionResult.ErrorOutput) {
            Write-Log "Script error output: $($ExecutionResult.ErrorOutput)" "ERROR"
        }
        exit 1
    }
    
    Write-Log "=== Trigger Staging Monitor Completed Successfully ===" "SUCCESS"
    exit 0
    
} catch {
    Write-Log "Unexpected error in main execution: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 1
}
