import requests
from datetime import datetime
url = "http://vwmazidr2023q2.fisdev.local:100/api/v1/requests"
timeStr = datetime.now().strftime('%Y-%m-%dT%H:%M:%S%z')
request_body = {
    "data": {
        "type": "requests",
        "attributes": {
            "requestDescription": "ALS results loading test",
            "resultsLocation": "\\\\vwmazpswkspace.fisdev.local\\Data\\Projects\\Internal\\BiProjects\\PyCodes\\DirectModePackage\\results",
            "maxInstructions": 10,
            "requestType": "direct",
            "sourceType": "FRAPI",
            "resultsDbVaultSectionName": "resultsdb",
            "submitted-date": "2024-12-31T08:46:27",
            "priority": 1,
            "statusId": 3,
            "connectionTechnology": "PgSql",
            "requestFilters": {
            "fileNames": [],
            "runNumbers": [20],
            "products": ["A_EURO"],
            "resultsTypes": ["prj"],
            "monthFirst": 1,
            "monthTimes": 51
            }
        }
    }
}
print(request_body)

response=requests.post(url,json=request_body,headers = {"Content-Type": "application/vnd.api+json"})

print(response.status_code)
